import { createSlice } from '@reduxjs/toolkit'

export const drawerSlice = createSlice({
    name: 'drawerSlice',
    initialState: {
        value: {
            open: true,
            selectedMenuId: "menu_overview"
        },
    },
    reducers: {
        open: (state) => {
            state.value.open = true
        },
        close: (state) => {
            state.value.open = false
        },
        toggle: (state) => {
            state.value.open = !state.value.open
        },
        setSelectedMenuId: (state, action) => {
            state.value.selectedMenuId = action.payload
        }
    },
});

// Action creators are generated for each case reducer function
export const { open, close, toggle, setSelectedMenuId } = drawerSlice.actions

export default drawerSlice.reducer