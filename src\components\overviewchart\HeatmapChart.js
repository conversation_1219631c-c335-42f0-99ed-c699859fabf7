import React, { useEffect, useState } from 'react'

const HeatmapChart = () => {
    const yLabels = ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON>i", "Sat"];
    const xLabels = ["12.00", "13.00", "14.00", "15.00", "16.00", "17.00", "18.00"]
    const [heatmapData, setHeatmapData] = useState([
        []
    ])
    const data = new Array(yLabels.length)
        .fill(0)
        .map(() =>
            new Array(xLabels.length)
                .fill(0)
                .map(() => Math.floor(Math.random() * 1000))
        )

    useEffect(() => {
        createHeatmapData();
    }, [])

    const createHeatmapData = () => {
        let maxValue = 0;
        let minValue = data[0][0];
        for (let i = 0; i < data.length; i++) {
            for (let j = 0; j < data[i].length; j++) {
                if (data[i][j] > maxValue) {
                    maxValue = data[i][j]
                }
                if (data[i][j] < minValue) {
                    minValue = data[i][j]
                }
            }
        }
        let tempList = [];
        for (let i = 0; i < data.length; i++) {
            let tempRow = []
            for (let j = 0; j < data[i].length; j++) {

                const percentageVal = data[i][j] / maxValue * 100;
                // console.log("percentageVal", percentageVal)
                tempRow.push({ value: data[i][j], percentage: percentageVal });
            }
            tempList.push(tempRow);
        }
        // console.log("data", data);
        // console.log("maxValue", maxValue);
        // console.log("minValue", minValue);
        setHeatmapData(tempList);
    }

    return (
        <div className="heatmap-container">
            {heatmapData.map((row, rowIndex) => (
                <div key={rowIndex} className="heatmap-row d-flex align-items-center">
                    <div className='heatmap-yLabels '>{yLabels[rowIndex]}</div>
                    {row.map((cell, cellIndex) => (
                        <div key={cellIndex} className=" d-flex align-items-center flex-column justify-content-center"
                            style={{ position: 'relative' }}>
                            <div
                                className="heatmap-cell"
                                style={{
                                    backgroundColor: `rgba(252, 103, 103, ${cell.percentage / 100})`,
                                }}
                            >
                                {/* <span className="heatmap-cell-value">{cell.value}</span> */}
                            </div>
                            {
                                (heatmapData.length - 1) == rowIndex ?
                                    <div className='heatmap-xLabels'>
                                        {xLabels[cellIndex]}
                                    </div>
                                    : null

                            }

                        </div>
                    ))}
                </div>
            ))}
        </div>
    )
}

export default HeatmapChart