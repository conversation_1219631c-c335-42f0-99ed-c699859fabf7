import { alpha, Paper } from '@mui/material';
import React from 'react'
import { LineChart, lineElementClasses } from '@mui/x-charts/LineChart';

const FollowersGrowthChart = () => {
    const pData = [2400, 1398, 9800, 3908, 4800, 3800, 4300, 10000, 1000, 7500, 65];
    const xLabels = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
    ];
    const CustomToolTip = (props) => {
        const { itemData, series, } = props;
        return (
            <Paper sx={{ backgroundColor: "#0A0A0C", borderRadius: "8px" }}>
                <p className='lineChartTooltip__text'>{series.data[itemData.dataIndex]}</p>
            </Paper>
        )
    }
    return (
        <>
            <LineChart
                xAxis={[{
                    scaleType: 'point', data: xLabels,

                    tickFormatter: (value) => {
                        return value;
                    }

                }]}
                series={[
                    {
                        id: "follower", data: pData, area: 'true', color: "#FC6767", curve: "linear",
                        showMark: false,
                        stackOrder: "ascending",
                    },
                ]}
                height={220}
                // yAxis={[{ scaleType: "linear", tickMaxStep: 20 }]}
                margin={{ left: 50, right: 30, top: 30, bottom: 30 }}
                grid={{ vertical: false, horizontal: true }}
                yAxis={[{ min: 0, max: 15000, }]}
                // tooltip={{
                //     trigger: "item", itemContent: CustomToolTip
                // }}
                sx={{
                    display: "flex",
                    height: "100%",
                    paddingRight: "6px",
                    alignItems: "flex-start",
                    gap: "10px",
                    flexShrink: 0,
                    "& .MuiAreaElement-series-follower": {
                        fill: alpha("#FC6767", 0.1),
                    },
                    "& .MuiLineElement-root": {
                        strokeWidth: 2,
                    },
                    "& .MuiMarkElement-root": {
                        scale: "0.6",
                        fill: "#fff",
                        strokeWidth: 0,
                    },
                    //change left yAxis label styles
                    "& .MuiChartsAxis-left .MuiChartsAxis-tickLabel": {
                        fill: "#222222",
                        fontSize: "10px",
                        fontStyle: "normal",
                        lineHeight: "14px",
                        opacity: 0.5,
                    },

                    // change bottom label styles
                    "& .MuiChartsAxis-bottom .MuiChartsAxis-tickLabel": {
                        fill: "#222222",
                        fontSize: "10px",
                        fontStyle: "normal",
                        lineHeight: "14px",
                        opacity: 0.5,
                    },
                    // bottomAxis Line Styles
                    "& .MuiChartsAxis-bottom .MuiChartsAxis-line": {
                        stroke: "#222222",
                        strokeWidth: 0,
                    },
                    // leftAxis Line Styles
                    "& .MuiChartsAxis-left .MuiChartsAxis-line": {
                        stroke: "#222222",
                        strokeWidth: 0,
                    },
                    "& .MuiChartsAxisHighlight-root": {
                        strokeDasharray: 5,
                        strokeWidth: 0.6,
                    },
                    "& .MuiChartsGrid-horizontalLine": {
                        strokeDasharray: 5,
                        strokeWidth: 0.4,
                    },
                }}
            />
        </>
    )
}

export default FollowersGrowthChart