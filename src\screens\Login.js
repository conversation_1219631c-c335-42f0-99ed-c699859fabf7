import { Box, Button, Grid } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { openSnackBar } from '../state/slice/snackbarSlice';
import { AppButton, AppOutlineButton } from '../components/ui_component/AppButton';
import { AppInput } from '../components/ui_component/AppInput';
import AppLogo from "../assets/images/logo.png"
import { setSelectedMenuId } from '../state/slice/drawerSlice';
import AppRoutes from '../data/AppRoutes';
import { checkValueLength } from '../util/StringUtils';
import { closeLoader, showLoader } from '../state/slice/loaderSlice';
import { generateOtpApi, loginApi } from '../api/Apis';
import { saveUserCredential } from '../storage/auth.login';
import { login } from '../state/slice/loginSlice';

const Login = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [loginPageType, setLoginPageType] = useState("USER_NAME");
    const [minutes, setMinutes] = useState(1);
    const [seconds, setSeconds] = useState(30);
    const [disableResendOtpBtn, setDisableResendOtpBtn] = useState(true);

    const [otpMessage, setOtpMessage] = useState("")

    const [loginInputs, setLoginInputs] = useState({
        userId: "",
        password: "",
        userIdErr: "",
        passwordErr: ""
    });
    const [pageError, setPageError] = useState({ title: "", message: "" });
    useEffect(() => {
        let myInterval = setInterval(() => {
            if (seconds > 0) {
                setSeconds(seconds - 1);
            }
            if (seconds === 0) {
                if (minutes === 0) {
                    clearInterval(myInterval)
                } else {
                    setMinutes(minutes - 1);
                    setSeconds(59);
                }
            }
            if (minutes == 0 && seconds == 0) {
                setDisableResendOtpBtn(false)
            }
        }, 1000)
        return () => {
            clearInterval(myInterval);
        };

    }, [seconds, minutes]);
    const handleSignIn = () => {
        if (isFormValid()) {
            if (loginPageType == "USER_NAME") {
                // Call Service for OTP
                dispatch(showLoader());
                generateOtpService();

            }
            if (loginPageType == "OTP") {
                // Submit the OTP, after success go to Home Page
                dispatch(showLoader());
                appLoginService();

            }

        }
    };
    const handleInputChange = (name) => (value) => {
        setLoginInputs(prevState => ({
            ...prevState, [name]: value, [name + "Err"]: ""
        }));
    }
    const isFormValid = () => {
        let isValid = true;
        let errorMsgData = {
            userIdErr: "",
            passwordErr: ""
        }
        if (loginPageType == "USER_NAME") {
            if (!checkValueLength(loginInputs.userId)) {
                errorMsgData.userIdErr = "required";
                isValid = false;
            }
        }
        if (loginPageType == "OTP") {
            if (!checkValueLength(loginInputs.password)) {
                errorMsgData.passwordErr = "required";
                isValid = false;
            }
        }
        setLoginInputs(prevState => ({
            ...prevState, ...errorMsgData
        }));
        return isValid;

    }
    const appLoginService = () => {
        loginApi(loginInputs.userId, loginInputs.password).then(result => {
            dispatch(closeLoader());
            if (result.isSuccess) {
                const userDetails = {
                    _user_id: loginInputs.userId,
                    _password: loginInputs.password,
                    _userSeq: result.data.data.user_seq,
                    _profile_seq: result.data.data.profile_seq,
                }
                dispatch(login());
                saveUserCredential(userDetails);
                dispatch(setSelectedMenuId("menu_overview"))
                navigate("../playlist/overview", { replace: true })
                // console.log("userDetails", userDetails);

            }
            else {
                if (result.data && result.data.err_cd == "E006" && result.data.data) {
                    let errorMsgData = {
                        userIdErr: "",
                        passwordErr: ""
                    }
                    if (result.data.data.user_id) {
                        errorMsgData.userIdErr = result.data.data.user_id;
                    }
                    if (result.data.data.password) {
                        errorMsgData.passwordErr = result.data.data.password;
                    }
                    setLoginInputs(prevState => ({
                        ...prevState, ...errorMsgData
                    }));
                }
                else {
                    dispatch(openSnackBar({
                        open: true,
                        message: result.message,
                        type: "FAILED"

                    }))
                }
            }
        });
    }
    const generateOtpService = () => {
        generateOtpApi(loginInputs.userId).then(result => {
            dispatch(closeLoader());
            if (result.isSuccess) {
                setLoginInputs(prevState => ({
                    ...prevState, ["password"]: "", ["passwordErr"]: ""
                }));
                setOtpMessage(result.message);
                resetTimer();
                setLoginPageType("OTP");
            }
            else {
                if (result.data && result.data.err_cd == "E006" && result.data.data) {
                    let errorMsgData = {
                        userIdErr: "",
                        passwordErr: ""
                    }
                    if (result.data.data.user_id) {
                        errorMsgData.userIdErr = result.data.data.user_id;
                    }
                    if (result.data.data.password) {
                        errorMsgData.passwordErr = result.data.data.password;
                    }
                    setLoginInputs(prevState => ({
                        ...prevState, ...errorMsgData
                    }));
                }
                else {
                    dispatch(openSnackBar({
                        open: true,
                        message: result.message,
                        type: "FAILED"

                    }))
                }
            }
        });
    }
    const handleBackPress = () => {
        setOtpMessage("")
        setLoginPageType("USER_NAME");
    }
    const resetTimer = () => {
        setDisableResendOtpBtn(true);
        setSeconds(30);
        setMinutes(1);
    }
    const resendOtpBtnPress = () => {
        if (!disableResendOtpBtn) {
            resetTimer();
        }

    }
    return (
        <Grid container
            justifyContent="center"
            alignItems="center">
            <Grid item xs={10} sm={6} md={4} lg={3} >
                <Box style={styles.inputContainer}>
                    <div className='text-center'>
                        <img src={AppLogo} alt="App Logo" className='img-fluid loginLogo' />
                    </div>
                    {
                        loginPageType == "USER_NAME" ?
                            <div className='text-center promptText'>
                                Please enter your Mobile Number / Email Id linked to your SoTrue account!
                            </div>
                            : null
                    }
                    {
                        loginPageType == "OTP" ?
                            <div className='text-center promptText'>
                                {otpMessage}
                            </div>
                            : null
                    }
                    {
                        loginPageType == "USER_NAME" ?
                            <Box>
                                <AppInput
                                    label='Mobile Number / Email Id'
                                    placeholder='Type here'
                                    value={loginInputs.userId}
                                    errorMessage={loginInputs.userIdErr}
                                    onEnterPressed={() => handleSignIn()}
                                    onTextChange={handleInputChange('userId')}
                                    marginBottom='22px' />
                            </Box>
                            : null
                    }
                    {
                        loginPageType == "OTP" ?
                            <Box>
                                <AppInput
                                    label='OTP'
                                    placeholder='Type here'
                                    value={loginInputs.password}
                                    errorMessage={loginInputs.passwordErr}
                                    onEnterPressed={() => handleSignIn()}
                                    isPasswordField={false}
                                    onTextChange={handleInputChange('password')} />
                                <div className='d-flex'>
                                    <div className='resendOtpText'>
                                        Resend OTP in
                                    </div>
                                    <div className='resendOtpBtnBox d-flex'>
                                        <div className='resendOtpBtnBox__timer'>
                                            {minutes}:{seconds < 10 ? `0${seconds}` : seconds}
                                        </div>
                                        <div className={`resendOtpBtnBox__btn ${disableResendOtpBtn ? "disabled" : ""}`}
                                            onClick={() => resendOtpBtnPress()}> Resend OTP</div>
                                    </div>
                                </div>
                            </Box>
                            : null
                    }
                    <Box sx={{
                        marginTop: "32px",
                    }}>
                        {
                            loginPageType == "USER_NAME" ?
                                <AppButton label='Submit' onClick={() => handleSignIn()} />
                                : null
                        }
                        {
                            loginPageType == "OTP" ?
                                <div className='d-flex'>
                                    <AppOutlineButton label='Back' style={{ marginRight: "10px" }} onClick={() => handleBackPress()} />
                                    <AppButton label='Login' onClick={() => handleSignIn()} />
                                </div>

                                : null
                        }
                    </Box>
                </Box>
            </Grid>

        </Grid>
    )
}
const styles = {
    inputContainer: {
        marginTop: "5rem"
    }
}

export default Login