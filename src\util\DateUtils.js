export function dateDbFormat(_date) {
    let converted = new Date(_date);
    var month = converted.getMonth() + 1;
    var day = converted.getDate();
    var year = converted.getFullYear();
    return year + "-" + (month.toString().length === 1 ? "0" + month.toString() : month) + "-" + (day.toString().length === 1 ? "0" + day.toString() : day);
}
export function dbTimeFormat(_time) {
    let converted = new Date(_time);
    var hours = converted.getHours();
    var minutes = converted.getMinutes();
    var seconds = converted.getSeconds();
    if (isNaN(hours)) {
        return "";
    }
    let hoursD = hours.toString().length === 1 ? "0" + hours.toString() : hours;
    return hoursD + ":" + (minutes.toString().length === 1 ? "0" + minutes.toString() : minutes) + ":" + (seconds.toString().length === 1 ? "0" + seconds.toString() : seconds);
}
export function DBDateTimeFormat(_timestamp) {
    let timeStampVal = dateDbFormat(_timestamp) + " " + dbTimeFormat(_timestamp)
    return timeStampVal;
}
export function DisplayDateFormat(dateVal) {
    const year = new Intl.DateTimeFormat('en', { year: 'numeric' }).format(dateVal);
    const month = new Intl.DateTimeFormat('en', { month: 'short' }).format(dateVal);
    const da = new Intl.DateTimeFormat('en', { day: '2-digit' }).format(dateVal);
    let formatDate = da + '-' + month + '-' + year;
    return formatDate;
}
export function DisplayTimeFormat(dateVal) {
    let date = new Date(dateVal);
    let formatter = new Intl.DateTimeFormat('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
    let formattedTime = formatter.format(date);
    return formattedTime;
}
export function displayDbTimeFormat(_time) {
    var timeData = (_time || '').split(':')
    var hours = timeData[0];
    var minutes = timeData[1];
    var minutesData = timeData[1];
    var ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12;
    if (parseInt(minutes) === 0) {
        minutesData = minutes;
    }
    // else if (parseInt(minutes) < 10) {
    //     minutesData = '0' + minutes;
    // }
    return hours + ':' + minutesData + ' ' + ampm;
}

export function DisplayDateTimeFormat(_timestamp) {
    let timeStampVal = DisplayDateFormat(_timestamp) + " " + DisplayTimeFormat(_timestamp)
    return timeStampVal;
}