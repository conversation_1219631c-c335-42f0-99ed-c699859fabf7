
import { Button, Dialog, DialogActions, DialogContent, Grid } from '@mui/material';
import React, { Component, useEffect, useState } from 'react'


const VideoPopup = ({
    displayPopup = false,
    videoUrl = null,
    videoPoster = null,
    autoPlay = true,
}) => {
    const [openPopup, setOpenPopup] = useState(false);
    const [videoUrlValue, setVideoUrlValue] = useState(videoUrl);
    const [videoPosterValue, setVideoPosterValue] = useState(videoPoster);
    const [autoPlayValue, setAutoPlayValue] = useState(autoPlay);
    useEffect(() => {
        setOpenPopup(displayPopup)
    }, [displayPopup])


    const handleModalShowHide = () => {
        setOpenPopup(false);
    }

    return (
        <Dialog
            fullWidth={true}
            maxWidth="md"
            open={openPopup}
            onClose={() => handleModalShowHide()}>
            <DialogContent>
                <Grid container>
                    <Grid item xs={12}>
                        <div className="videoBox">
                            <video poster={videoPosterValue}
                                controls
                                autoPlay={autoPlayValue}
                                playsInline
                                src={videoUrlValue} />

                        </div>
                    </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                <Button autoFocus onClick={() => handleModalShowHide()} color="primary">
                    Close
                </Button>
            </DialogActions>
        </Dialog>
    )
}

export default VideoPopup