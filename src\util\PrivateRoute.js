import { Navigate, useLocation } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux";
import Home from "../screens/Home";
import { login } from "../state/slice/loginSlice";
import { getUserCredential } from "../storage/auth.login";

const PrivateRoute = () => {
    let location = useLocation();
    const dispatch = useDispatch();
    const isLoggedIn = useSelector((state) => state.auth.value.isLoggedIn);
    if (!isLoggedIn) {
        let userDetails = getUserCredential();
        if (userDetails !== null) {
            dispatch(login());
        }
        else {
            return <Navigate to="/login" state={{ from: location }} replace />;
        }
    }
    return <Home />;

}
export default PrivateRoute;