import { Grid, IconButton } from "@mui/material";
import React, { useEffect, useState } from "react";
import ImagePlaceholder from "../../assets/images/default_image.jpg";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import PlaylistItem from "../../components/contentscreen/PlaylistItem";
import AppRoutes from "../../data/AppRoutes";
import EditRoundedIcon from "@mui/icons-material/EditRounded";
import { closeLoader, showLoader } from "../../state/slice/loaderSlice";
import { openSnackBar } from "../../state/slice/snackbarSlice";
import {
  getAllEpisodeApi,
  getAllSeasonsApi,
  getAllShowApi,
} from "../../api/Apis";
import InfoRoundedIcon from "@mui/icons-material/InfoRounded";
import VisibleErrorMessage from "../../components/ui_component/VisibleErrorMessage";
import EDIT_ICON from "../../assets/images/svgs/Edit.svg";
import INFO_ICON from "../../assets/images/svgs/Info.svg";

const ContentScreen = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [showList, setShowList] = useState([]);
  const [seasonList, setSeasonList] = useState([]);
  const [episodeList, setEpisodeList] = useState([]);

  const [showDataErrorMessage, setShowDataErrorMessage] = useState("");
  const [episodeDataErrorMessage, setEpisodeDataErrorMessage] = useState("");

  const [selectedShowSeq, setSelectedShowSeq] = useState(-1);
  const [selectedSeasonSeq, setSelectedSeasonSeq] = useState(-1);

  const [showEpisodeBox, setShowEpisodeBox] = useState(false);

  useEffect(() => {
    allShowService();
  }, []);

  const allShowService = () => {
    dispatch(showLoader());
    getAllShowApi().then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        setShowDataErrorMessage("");
        setShowList(result.data.data);
      } else {
        setShowList([]);
        setShowDataErrorMessage(result.message);
      }
    });
  };

  const ParentPlaylistItem = ({ isSelected = false, index = 0, data = {} }) => {
    return (
      <div
        className={`parentPlaylistItem ${
          isSelected ? "selected" : ""
        } d-flex align-items-center justify-content-center mb-3`}
      >
        <img
          src={data.thumb_file}
          alt="item"
          className="parentPlaylistItem__image"
        />
        <div
          className="playlistOverlay"
          onClick={() => parentPlaylistItemClick("ITEM", index)}
        />
        <div className="actionBox">
          <div className="actionBox__iconBox">
            <img
              src={EDIT_ICON}
              className="editIcon me-2"
              alt="EditIcon"
              onClick={() => parentPlaylistItemClick("EDIT", index)}
            />
            <img
              src={INFO_ICON}
              className="editIcon"
              alt="EditIcon"
              onClick={() => parentPlaylistItemClick("INFO", index)}
            />
          </div>
        </div>
      </div>
    );
  };
  const SeasonListItem = ({ isSelected = false, index = 0, data = {} }) => {
    return (
      <div
        className={`parentPlaylistItem ${
          isSelected ? "selected" : ""
        } d-flex align-items-center justify-content-center mb-3`}
      >
        <img
          src={data.cover_image}
          alt="item"
          className="parentPlaylistItem__image"
        />
        <div
          className="playlistOverlay"
          onClick={() => seasonListItemClick("ITEM", index)}
        />
        <div className="actionBox">
          <div className="actionBox__iconBox">
            <img
              src={EDIT_ICON}
              className="editIcon me-2"
              alt="EditIcon"
              onClick={() => seasonListItemClick("EDIT", index)}
            />
          </div>
        </div>
      </div>
    );
  };
  const parentPlaylistItemClick = (type, index) => {
    if (type == "INFO") {
      const detailsData = showList[index];
      navigate(AppRoutes.CONTENT_DETAILS_PATH, {
        state: { showSeq: detailsData.show_seq },
      });
    } else if (type == "ITEM") {
      const detailsData = showList[index];
      setSelectedShowSeq(detailsData.show_seq);
      showSeasonService(detailsData.show_seq);

      // navigate(AppRoutes.CONTENT_DETAILS_PATH, { state: { showSeq: detailsData.show_seq } });
    } else if (type == "EDIT") {
      const detailsData = showList[index];
      navigate(AppRoutes.ADD_SHOW_PATH, {
        state: { showSeq: detailsData.show_seq },
      });
    }

    // navigate(AppRoutes.ADD_SHOW_PATH, { state: { showSeq: 2 } });
  };
  const showSeasonService = (showSeq) => {
    dispatch(showLoader());
    getAllSeasonsApi(showSeq).then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        setShowEpisodeBox(false);
        setSeasonList(result.data.data);
      } else {
        setSeasonList([]);
        setSelectedSeasonSeq(-1);
        setShowEpisodeBox(true);
        seasonEpisodesService(showSeq, -1);
      }
    });
  };
  const seasonEpisodesService = (showSeq, seasonSeq) => {
    dispatch(showLoader());
    getAllEpisodeApi(showSeq, seasonSeq).then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        setEpisodeDataErrorMessage("");
        setEpisodeList(result.data.data);
      } else {
        setEpisodeList([]);
        setEpisodeDataErrorMessage(result.message);
      }
    });
  };
  const seasonListItemClick = (type, index) => {
    if (type == "EDIT") {
      const detailsData = seasonList[index];
      navigate(AppRoutes.ADD_SEASON_PATH, {
        state: { seasonSeq: detailsData.season_seq, showSeq: selectedShowSeq },
      });
    } else if (type == "ITEM") {
      const detailsData = seasonList[index];
      setSelectedSeasonSeq(detailsData.season_seq);
      setShowEpisodeBox(true);
      seasonEpisodesService(selectedShowSeq, detailsData.season_seq);

      // navigate(AppRoutes.CONTENT_DETAILS_PATH, { state: { showSeq: detailsData.show_seq } });
    }
  };
  const playlistItemClick = (idVal) => {
    // Navigate to episode details page with correct state key
    navigate(AppRoutes.EPISODE_DETAILS_PATH, {
      state: { episodeSeq: idVal },
    });
  };
  return (
    <>
      <Grid container>
        <Grid item xs={12}>
          <div className="formBoxTitle">Show</div>
        </Grid>
        <Grid item xs={12}>
          <div className="d-flex mt-3 flex-wrap">
            {showList.map((obj, i) => {
              return (
                <ParentPlaylistItem
                  key={i}
                  index={i}
                  isSelected={String(obj.show_seq) == String(selectedShowSeq)}
                  data={obj}
                />
              );
            })}
            {showDataErrorMessage.length > 0 ? (
              <VisibleErrorMessage errorMessage={showDataErrorMessage} />
            ) : null}
          </div>
        </Grid>
        {seasonList.length > 0 ? (
          <>
            <Grid item xs={12} mt={4}>
              <div className="formBoxTitle">Season</div>
            </Grid>
            <Grid item xs={12}>
              <div className="d-flex mt-3 flex-wrap">
                {seasonList.map((obj, i) => {
                  return (
                    <SeasonListItem
                      key={i}
                      isSelected={
                        String(obj.season_seq) == String(selectedSeasonSeq)
                      }
                      index={i}
                      data={obj}
                    />
                  );
                })}
              </div>
            </Grid>
          </>
        ) : null}
        {showEpisodeBox ? (
          <>
            <Grid item xs={12} mt={3}>
              <div className="formBoxTitle">Episodes ...</div>
            </Grid>
            <Grid item xs={12}>
              <div className="d-flex mt-3 flex-wrap">
                {episodeList.map((obj, i) => {
                  return (
                    <PlaylistItem
                      key={i}
                      id={obj.episode_seq} // Pass the correct id prop
                      index={i}
                      data={obj}
                      playlistItemClick={playlistItemClick}
                    />
                  );
                })}
                {episodeDataErrorMessage.length > 0 ? (
                  <VisibleErrorMessage errorMessage={episodeDataErrorMessage} />
                ) : null}
              </div>
            </Grid>
          </>
        ) : null}
      </Grid>
    </>
  );
};

export default ContentScreen;
