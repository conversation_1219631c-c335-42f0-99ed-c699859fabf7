import React, { useRef, useState } from 'react'
import AddPhotoAlternateRoundedIcon from '@mui/icons-material/AddPhotoAlternateRounded';
import DeleteOutlineRoundedIcon from '@mui/icons-material/DeleteOutlineRounded';
import { checkValueLength } from '../../util/StringUtils';
import RemoveRedEyeRoundedIcon from '@mui/icons-material/RemoveRedEyeRounded';
import ViewImagePopup from './ViewImagePopup';


const AppMediaSelection = ({
    headings = "",
    secondHeading = "",
    selectedMedia = null,
    onMediaChange = null,
    displayMediaFile = null,
    mediaErr = "",
    mediaFileName = "",
    noDeleteBtn = false,
    acceptFileType = "image/*"
}) => {
    const mediaRefPress = useRef(null);
    const onInputChange = (e) => {
        if (onMediaChange) {
            onMediaChange("SELECT_MEDIA", { mediaData: e.target.files });
        }
    }
    const mediaBtnPress = () => {
        mediaRefPress.current.click();
    }
    const deleteMediaBtnPress = () => {
        if (onMediaChange) {
            onMediaChange("DELETE_MEDIA", null)
        }
    }
    const [showImagePopupData, setShowImagePopupData] = useState({
        refreshKey: 1,
        displayPopup: false,
        imageUrl: null,
    })
    const viewMediaBtnPress = () => {
        setShowImagePopupData(prevState => ({
            ...prevState,
            refreshKey: Math.random(),
            displayPopup: true,
            imageUrl: displayMediaFile,
        }));
    }
    return (
        <div className="media-selection-container">
            <div className='d-flex align-items-center inputMediaBox'>
                <input
                    ref={mediaRefPress}
                    className="d-none"
                    onClick={(event) => {
                        event.target.value = null
                    }}
                    accept={acceptFileType}
                    type="file"
                    onChange={(e) => onInputChange(e)} />

                <div className='d-flex align-items-center justify-content-center inputMediaBox__iconBox'
                    onClick={() => mediaBtnPress()}>
                    {
                        displayMediaFile != null ?
                            <>
                                <img src={displayMediaFile} alt="media" className='inputMediaBox__media' />
                            </>

                            :
                            <AddPhotoAlternateRoundedIcon fontSize='medium' />
                    }

                </div>
                <div className='d-flex flex-column ms-3 flex-grow-1 flex-wrap'>
                    {

                        checkValueLength(mediaFileName) ?
                            <div className='inputMediaBox__headings--filename'>{mediaFileName}</div>
                            :
                            <>
                                <div className='inputMediaBox__headings'>{headings}</div>
                                <div className='inputMediaBox__secondHeading'>{secondHeading}</div>
                            </>

                    }

                </div>

                <div className='d-flex  ps-3 inputMediaBox__deleteIcon'>
                    {
                        displayMediaFile != null ?
                            <div className='me-2'>
                                <RemoveRedEyeRoundedIcon fontSize='medium' onClick={() => viewMediaBtnPress()} />
                            </div>
                            : null
                    }
                    {
                        selectedMedia != null && !noDeleteBtn ?
                            <DeleteOutlineRoundedIcon fontSize='medium' onClick={() => deleteMediaBtnPress()} />
                            :
                            null
                    }
                </div>

            </div>
            {
                mediaErr.length != 0 ?
                    <div className='text-danger mt-2'>{mediaErr}</div>
                    :
                    null
            }
            <ViewImagePopup
                key={showImagePopupData.refreshKey}
                displayPopup={showImagePopupData.displayPopup}
                imageUrl={showImagePopupData.imageUrl}
            />

        </div>
    )
}

export default AppMediaSelection