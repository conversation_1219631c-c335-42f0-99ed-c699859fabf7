import { Grid, Paper } from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  AppInput,
  AppTextareaInput,
} from "../../components/ui_component/AppInput";
import AppMediaSelection from "../../components/ui_component/AppMediaSelection";
import { AppSelect } from "../../components/ui_component/AppSelect";
import { AppSwitch } from "../../components/ui_component/AppSwitch";
import CurrencyRupeeRoundedIcon from "@mui/icons-material/CurrencyRupeeRounded";
import {
  AppButton,
  AppOutlineButton,
} from "../../components/ui_component/AppButton";
import UploadGuideComponent from "../../components/common/UploadGuideComponent";
import LastSubmittedComponent from "../../components/common/LastSubmittedComponent";
import AppConstants from "../../data/AppConstants";
import AppDatePicker from "../../components/ui_component/AppDatePicker";
import AppDateTimePicker from "../../components/ui_component/AppDateTimePicker";
import { checkValueLength, decimalInputField } from "../../util/StringUtils";
import { closeLoader, showLoader } from "../../state/slice/loaderSlice";
import {
  AddUpdateShowApi,
  getInterestListApi,
  getLocationListApi,
  getMasterBannerApi,
  getShowDetailsApi,
  getYearOfReleaseListApi,
  setShowStatusApi,
} from "../../api/Apis";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { openSnackBar } from "../../state/slice/snackbarSlice";
import { dateDbFormat, DBDateTimeFormat } from "../../util/DateUtils";
import AppConfirmation from "../../components/ui_component/AppConfirmation";
import { AppMultiSelect } from "../../components/ui_component/AppMultiSelect";
import { AppMultiSelectSearch } from "../../components/ui_component/AppMultiSelectSearch";
import BackButton from "../../components/common/BackButton";
import AppVideoMediaSelection from "../../components/ui_component/AppVideoMediaSelection";
const AddShowScreen = ({}) => {
  const { state } = useLocation();
  const [inputValues, setInputValues] = useState({
    title: "",
    description: "",
    priceValue: "",
    location: "",
    expiryDate: null,
    scheduleTime: null,
    yearOfRelease: "",
    gridTitle: "",

    titleErr: "",
    descriptionErr: "",
    priceValueErr: "",
    locationErr: "",
    expiryDateErr: "",
    scheduleTimeErr: "",
    yearOfReleaseErr: "",
    gridTitleErr: "",
  });
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [topicsList, setTopicsList] = useState([]);
  const [selectedTopics, setSelectedTopics] = useState([]);
  const [locationList, setLocationList] = useState([]);
  const [yearOfReleaseList, setYearOfReleaseList] = useState([]);

  const [isMonetize, setIsMonetize] = useState(false);

  const [lastSubmittedData, setLastSubmittedData] = useState([
    {
      id: 1,
      title: "How to Design a Logotype",
      time: "Aug 21, 2021",
      type: "PUBLISHED",
    },
    {
      id: 2,
      title: "Adobe Illustrator Masterclass",
      time: "Aug 17, 2021",
      type: "PUBLISHED",
    },
    {
      id: 3,
      title: "Adobe Illustrator Masterclass",
      time: "Aug 17, 2021",
      type: "PUBLISHED",
    },
  ]);

  const [selectedPlaylistLogo, setSelectedPlaylistLogo] = useState(null);
  const [selectedPlaylistLogoFile, setSelectedPlaylistLogoFile] =
    useState(null);
  const [selectedPlaylistLogoFileName, setSelectedPlaylistLogoFileName] =
    useState("");
  const [selectedPlaylistLogoErr, setSelectedPlaylistLogoErr] = useState("");

  const [selectedThumbnailImage, setSelectedThumbnailImage] = useState(null);
  const [selectedThumbnailImageFile, setSelectedThumbnailImageFile] =
    useState(null);
  const [selectedThumbnailImageFileName, setSelectedThumbnailImageFileName] =
    useState("");
  const [selectedThumbnailImageErr, setSelectedThumbnailImageErr] =
    useState("");

  const [selectedPlaylistPreview, setSelectedPlaylistPreview] = useState(null);
  const [selectedPlaylistPreviewFile, setSelectedPlaylistPreviewFile] =
    useState(null);
  const [selectedPlaylistPreviewFileName, setSelectedPlaylistPreviewFileName] =
    useState(null);
  const [selectedPlaylistPreviewErr, setSelectedPlaylistPreviewErr] =
    useState("");

  const [selectedMasterBannerImage, setSelectedMasterBannerImage] =
    useState(null);
  const [selectedMasterBannerImageFile, setSelectedMasterBannerImageFile] =
    useState(null);
  const [
    selectedMasterBannerImageFileName,
    setSelectedMasterBannerImageFileName,
  ] = useState("");
  const [selectedMasterBannerImageErr, setSelectedMasterBannerImageErr] =
    useState("");

  const [selectedHomeFileImage, setSelectedHomeFileImage] = useState(null);
  const [selectedHomeImageFile, setSelectedHomeImageFile] = useState(null);
  const [selectedHomeImageFileName, setSelectedHomeImageFileName] =
    useState("");
  const [selectedHomeImageFileErr, setSelectedHomeImageFileErr] = useState("");

  const [selectedBannerFileImage, setSelectedBannerFileImage] = useState(null);
  const [selectedBannerImageFile, setSelectedBannerImageFile] = useState(null);
  const [selectedBannerImageFileName, setSelectedBannerImageFileName] =
    useState("");
  const [selectedBannerImageFileErr, setSelectedBannerImageFileErr] =
    useState("");

  const [masterBannerData, setMasterBannerData] = useState({
    bannerImageName: null,
    bannerImageFile: null,
  });

  const [dbFileData, setDbFileData] = useState({});

  const [confirmation, setConfirmation] = React.useState({
    open: false,
    title: "",
    message: "",
    onSubmit: null,
  });

  useEffect(() => {
    // getMasterBannerService();
    callShowDetailsService();
    getLocationListService();
    getTopicsListService();
    getYearOfReleaseListService();
  }, []);
  const getMasterBannerService = () => {
    getMasterBannerApi().then((result) => {
      if (result.isSuccess) {
        if (result.data.data.length > 0) {
          const dataObj = {
            bannerImageName: "Master Banner",
            bannerImageFile: result.data.data[0].playlist_master_image,
          };
          setMasterBannerData(dataObj);
          setSelectedMasterBannerImageFile(dataObj.bannerImageFile);
          setSelectedMasterBannerImageFileName(dataObj.bannerImageName);
        }
      }
    });
  };
  const getLocationListService = () => {
    dispatch(showLoader());
    getLocationListApi().then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        let tempList = [];
        result.data.data.map((item) => {
          tempList.push({ label: item, value: item });
        });
        setLocationList(tempList);
      } else {
        setLocationList([]);
      }
    });
  };
  const getTopicsListService = () => {
    dispatch(showLoader());
    getInterestListApi().then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        let tempList = [];
        result.data.data.map((item) => {
          tempList.push({
            label: item.interest_name,
            value: item.interest_name,
          });
        });
        setTopicsList(tempList);
      } else {
        setTopicsList([]);
      }
    });
  };
  const callShowDetailsService = () => {
    if (checkValueLength(state.showSeq) && state.showSeq != -1) {
      dispatch(showLoader());
      getShowDetailsApi(state.showSeq).then((result) => {
        dispatch(closeLoader());
        if (result.isSuccess) {
          const showData = result.data.data[0];
          let data = {
            title: showData.title,
            gridTitle: checkValueLength(showData.grid_title)
              ? showData.grid_title
              : "",
            description: showData.description,
            priceValue: checkValueLength(showData.price) ? showData.price : "",
            yearOfRelease: checkValueLength(showData.release_year)
              ? showData.release_year
              : "",
            location: checkValueLength(showData.location)
              ? showData.location
              : "",
            expiryDate: checkValueLength(showData.expiry_date)
              ? new Date(showData.expiry_date)
              : null,
            scheduleTime: checkValueLength(showData.scheduled_on)
              ? new Date(showData.scheduled_on)
              : null,
          };
          let topics = [];
          showData.topics.map((item) => {
            topics.push(item[0]);
          });
          setSelectedTopics(topics);
          const dataFile = {
            logo_file: showData.logo_file,
            thumb_file: showData.thumb_file,
            preview_file: showData.preview_file,
            home_file: showData.home_file,
            banner_file: showData.banner_file,
            logo_file_name: "show logo",
            thumb_file_name: "show thumb",
            preview_file_name: "show preview",
            home_file_name: "show home page Image",
            banner_file_name: "show banner image",
          };
          let isPaid = false;
          if (showData.is_paid == "YES") {
            isPaid = true;
          }
          setIsMonetize(isPaid);
          setDbFileData(dataFile);

          setSelectedPlaylistLogoFile(dataFile.logo_file);
          setSelectedThumbnailImageFile(dataFile.thumb_file);
          setSelectedPlaylistPreviewFile(dataFile.preview_file);
          setSelectedHomeImageFile(dataFile.home_file);
          setSelectedBannerImageFile(dataFile.banner_file);

          setSelectedPlaylistLogoFileName(dataFile.logo_file_name);
          setSelectedThumbnailImageFileName(dataFile.thumb_file_name);
          setSelectedPlaylistPreviewFileName(dataFile.preview_file_name);
          setSelectedHomeImageFileName(dataFile.home_file_name);
          setSelectedBannerImageFileName(dataFile.banner_file_name);
          setInputValues((prevState) => ({
            ...prevState,
            ...data,
          }));
        } else {
          dispatch(
            openSnackBar({
              open: true,
              message: result.message,
              type: "FAILED",
            })
          );
          // navigate(-1)
        }
      });
    }
  };

  const handleInputChange = (name) => (value) => {
    if (name == "expiryDate" || name == "scheduleTime") {
      setInputValues((prevState) => ({
        ...prevState,
        [name]: value,
        [name + "Err"]: "",
      }));
    } else if (name == "priceValue") {
      if (decimalInputField(value)) {
        setInputValues((prevState) => ({
          ...prevState,
          [name]: value,
          [name + "Err"]: "",
        }));
      }
    } else {
      setInputValues((prevState) => ({
        ...prevState,
        [name]: value,
        [name + "Err"]: "",
      }));
    }
  };
  const handleMonetize = () => {
    setIsMonetize(!isMonetize);
  };
  const playlistLogoClick = (clickID, obj) => {
    setSelectedPlaylistLogoErr("");
    if (clickID == "SELECT_MEDIA") {
      if (obj.mediaData.length != 0) {
        let mediaData = obj.mediaData[0];
        if (validateThumbnailImage(mediaData.type)) {
          if (mediaData.size <= AppConstants.DEFAULT_UPLOAD_IMAGE_SIZE) {
            setSelectedPlaylistLogo(mediaData);
            setSelectedPlaylistLogoFile(URL.createObjectURL(mediaData));
            setSelectedPlaylistLogoFileName(mediaData.name);
          } else {
            setSelectedPlaylistLogoErr("Image size should be less than 5MB");
          }
        } else {
          setSelectedPlaylistLogoErr("file format should be jpeg/png/jpg");
        }
      }
    } else if (clickID == "DELETE_MEDIA") {
      setSelectedPlaylistLogo(null);
      if (dbFileData.hasOwnProperty("logo_file")) {
        setSelectedPlaylistLogoFile(dbFileData.logo_file);
        setSelectedPlaylistLogoFileName(dbFileData.logo_file_name);
      } else {
        setSelectedPlaylistLogoFile(null);
        setSelectedPlaylistLogoFileName("");
      }
    }
  };
  const thumbnailImageClick = (clickID, obj) => {
    setSelectedThumbnailImageErr("");
    if (clickID == "SELECT_MEDIA") {
      if (obj.mediaData.length != 0) {
        let mediaData = obj.mediaData[0];
        if (validateThumbnailImage(mediaData.type)) {
          if (mediaData.size <= AppConstants.DEFAULT_UPLOAD_IMAGE_SIZE) {
            setSelectedThumbnailImage(mediaData);
            setSelectedThumbnailImageFile(URL.createObjectURL(mediaData));
            setSelectedThumbnailImageFileName(mediaData.name);
          } else {
            setSelectedThumbnailImageErr("Image size should be less than 5MB");
          }
        } else {
          setSelectedThumbnailImageErr("file format should be jpeg/png/jpg");
        }
      }
    } else if (clickID == "DELETE_MEDIA") {
      setSelectedThumbnailImage(null);
      if (dbFileData.hasOwnProperty("thumb_file")) {
        setSelectedThumbnailImageFile(dbFileData.thumb_file);
        setSelectedThumbnailImageFileName(dbFileData.thumb_file_name);
      } else {
        setSelectedThumbnailImageFile(null);
        setSelectedThumbnailImageFileName("");
      }
    }
  };

  const playlistPreviewClick = (clickID, obj) => {
    setSelectedPlaylistPreviewErr("");
    if (clickID == "SELECT_MEDIA") {
      if (obj.mediaData.length != 0) {
        let mediaData = obj.mediaData[0];
        const fileMaxSize =
          AppConstants.DEFAULT_UPLOAD_IMAGE_SIZE * 1024 * 1000;
        if (validateThumbnailImage(mediaData.type)) {
          if (mediaData.size <= fileMaxSize) {
            setSelectedPlaylistPreview(mediaData);
            setSelectedPlaylistPreviewFile(URL.createObjectURL(mediaData));
            setSelectedPlaylistPreviewFileName(mediaData.name);
          } else {
            setSelectedPlaylistPreviewErr("Image size should be less than 5MB");
          }
        } else {
          setSelectedPlaylistPreviewErr("file format should be jpeg/png/jpg");
        }
      }
    } else if (clickID == "DELETE_MEDIA") {
      setSelectedPlaylistPreview(null);
      if (dbFileData.hasOwnProperty("preview_file")) {
        setSelectedPlaylistPreviewFile(dbFileData.preview_file);
        setSelectedPlaylistPreviewFileName(dbFileData.preview_file_name);
      } else {
        setSelectedPlaylistPreviewFile(null);
        setSelectedPlaylistPreviewFileName("");
      }
    }
  };
  const masterBannerImageClick = (clickID, obj) => {
    setSelectedMasterBannerImageErr("");
    if (clickID == "SELECT_MEDIA") {
      if (obj.mediaData.length != 0) {
        let mediaData = obj.mediaData[0];
        if (validateThumbnailImage(mediaData.type)) {
          if (mediaData.size <= AppConstants.DEFAULT_UPLOAD_IMAGE_SIZE) {
            setSelectedMasterBannerImage(mediaData);
            setSelectedMasterBannerImageFile(URL.createObjectURL(mediaData));
            setSelectedMasterBannerImageFileName(mediaData.name);
          } else {
            setSelectedMasterBannerImageErr(
              "Image size should be less than 5MB"
            );
          }
        } else {
          setSelectedMasterBannerImageErr("file format should be jpeg/png/jpg");
        }
      }
    } else if (clickID == "DELETE_MEDIA") {
      setSelectedMasterBannerImage(null);
      if (masterBannerData.hasOwnProperty("bannerImageFile")) {
        setSelectedMasterBannerImage(masterBannerData.bannerImageFile);
        setSelectedMasterBannerImageFileName(masterBannerData.bannerImageName);
      } else {
        setSelectedMasterBannerImage(null);
        setSelectedMasterBannerImageFileName("");
      }
    }
  };
  const homeFileImageClick = (clickID, obj) => {
    setSelectedHomeImageFileErr("");
    if (clickID == "SELECT_MEDIA") {
      if (obj.mediaData.length != 0) {
        let mediaData = obj.mediaData[0];
        if (validateThumbnailImage(mediaData.type)) {
          if (mediaData.size <= AppConstants.DEFAULT_UPLOAD_IMAGE_SIZE) {
            setSelectedHomeFileImage(mediaData);
            setSelectedHomeImageFile(URL.createObjectURL(mediaData));
            setSelectedHomeImageFileName(mediaData.name);
          } else {
            setSelectedHomeImageFileErr("Image size should be less than 5MB");
          }
        } else {
          setSelectedHomeImageFileErr("file format should be jpeg/png/jpg");
        }
      }
    } else if (clickID == "DELETE_MEDIA") {
      setSelectedHomeFileImage(null);
      if (dbFileData.hasOwnProperty("home_file")) {
        setSelectedHomeImageFile(dbFileData.home_file);
        setSelectedHomeImageFileName(dbFileData.home_file_name);
      } else {
        setSelectedHomeImageFile(null);
        setSelectedHomeImageFileName("");
      }
    }
  };
  const bannerImageClick = (clickID, obj) => {
    setSelectedBannerImageFileErr("");
    if (clickID == "SELECT_MEDIA") {
      if (obj.mediaData.length != 0) {
        let mediaData = obj.mediaData[0];
        if (validateThumbnailImage(mediaData.type)) {
          if (mediaData.size <= AppConstants.DEFAULT_UPLOAD_IMAGE_SIZE) {
            setSelectedBannerFileImage(mediaData);
            setSelectedBannerImageFile(URL.createObjectURL(mediaData));
            setSelectedBannerImageFileName(mediaData.name);
          } else {
            setSelectedBannerImageFileErr("Image size should be less than 5MB");
          }
        } else {
          setSelectedBannerImageFileErr("file format should be jpeg/png/jpg");
        }
      }
    } else if (clickID == "DELETE_MEDIA") {
      setSelectedBannerFileImage(null);
      if (dbFileData.hasOwnProperty("banner_file")) {
        setSelectedBannerImageFile(dbFileData.banner_file);
        setSelectedBannerImageFileName(dbFileData.banner_file_name);
      } else {
        setSelectedBannerImageFile(null);
        setSelectedBannerImageFileName("");
      }
    }
  };
  const validateThumbnailImage = (mediaType) => {
    let validMedia = false;
    if (
      mediaType === "image/png" ||
      mediaType === "image/jpg" ||
      mediaType === "image/jpeg"
    ) {
      validMedia = true;
    }
    return validMedia;
  };

  const handleTopicSelection = (event) => {
    setSelectedTopics(event);
  };
  const formValid = () => {
    let isValid = true;
    let errorData = {
      titleErr: "",
      descriptionErr: "",
      priceValueErr: "",
      locationErr: "",
      expiryDateErr: "",
      scheduleTimeErr: "",
      yearOfReleaseErr: "",
      gridTitleErr: "",
    };
    if (state.showSeq == -1) {
      // if (selectedPlaylistLogo == null) {
      //     setSelectedPlaylistLogoErr("Please add the Playlist Logo");
      //     isValid = false;
      // }
      if (selectedThumbnailImage == null) {
        setSelectedThumbnailImageErr("Please add the Thumbnail Image");
        isValid = false;
      }
      if (selectedHomeFileImage == null) {
        setSelectedHomeImageFileErr("Please add the home Image");
        isValid = false;
      }
      if (selectedBannerFileImage == null) {
        setSelectedBannerImageFileErr("Please add the banner Image");
        isValid = false;
      }
      // if (selectedPlaylistPreview == null) {
      //     setSelectedPlaylistPreviewErr("Please add the Playlist Preview");
      //     isValid = false;
      // }
    }

    if (!checkValueLength(inputValues.title)) {
      errorData.titleErr = "Title is required";
      isValid = false;
    }
    if (!checkValueLength(inputValues.gridTitle)) {
      errorData.gridTitleErr = "Grid Title is required";
      isValid = false;
    }
    if (isMonetize) {
      if (!checkValueLength(inputValues.priceValue)) {
        errorData.priceValueErr = "Price is required";
        isValid = false;
      }
    }
    if (!checkValueLength(inputValues.description)) {
      errorData.descriptionErr = "Description is required";
      isValid = false;
    }
    setInputValues((prevState) => ({
      ...prevState,
      ...errorData,
    }));
    return isValid;
  };
  const saveBtnPress = () => {
    if (formValid()) {
      dispatch(showLoader());
      submitShowData();
    }
  };
  const submitShowData = () => {
    let is_paid = "NO";
    if (isMonetize) {
      is_paid = "YES";
    }
    let imageHasMap = [];
    // if (selectedPlaylistLogo != null) {
    //     imageHasMap.push({ inputName: "logo_file", imageData: selectedPlaylistLogo });
    // }
    if (selectedThumbnailImage != null) {
      imageHasMap.push({
        inputName: "thumb_file",
        imageData: selectedThumbnailImage,
      });
    }
    // if (selectedMasterBannerImage != null) {
    //     imageHasMap.push({ inputName: "master_file", imageData: selectedMasterBannerImage });
    // }
    // if (selectedPlaylistPreview != null) {
    //     imageHasMap.push({ inputName: "preview_file", imageData: selectedPlaylistPreview });
    // }
    if (selectedHomeFileImage != null) {
      imageHasMap.push({
        inputName: "home_file",
        imageData: selectedHomeFileImage,
      });
    }
    if (selectedBannerFileImage != null) {
      imageHasMap.push({
        inputName: "banner_file",
        imageData: selectedBannerFileImage,
      });
    }

    let priceValue = "";
    if (isMonetize) {
      priceValue = inputValues.priceValue;
    }
    let expiryDate = "";
    if (checkValueLength(inputValues.expiryDate)) {
      expiryDate = encodeURIComponent(dateDbFormat(inputValues.expiryDate));
    }
    let scheduleOn = "";
    if (checkValueLength(inputValues.scheduleTime)) {
      scheduleOn = encodeURIComponent(
        DBDateTimeFormat(inputValues.scheduleTime)
      );
    }
    let locationValue = "";
    if (checkValueLength(inputValues.location)) {
      locationValue = encodeURIComponent(inputValues.location);
    }
    let titleVal = encodeURIComponent(inputValues.title);
    let gridTitle = encodeURIComponent(inputValues.gridTitle);
    let descriptionVal = encodeURIComponent(inputValues.description);
    let showSeq = state.showSeq;
    let topics = [];
    if (selectedTopics.length != 0) {
      topics = encodeURIComponent(JSON.stringify(selectedTopics));
    }
    let yearOfRelease = "";
    if (checkValueLength(inputValues.yearOfRelease)) {
      yearOfRelease = inputValues.yearOfRelease;
    }

    AddUpdateShowApi(
      showSeq,
      titleVal,
      gridTitle,
      descriptionVal,
      is_paid,
      priceValue,
      expiryDate,
      scheduleOn,
      locationValue,
      topics,
      yearOfRelease,
      imageHasMap
    ).then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "SUCCESS",
          })
        );
        if (showSeq == -1) {
          navigate(-1);
        }
      } else {
        let errorMsgShow = false;
        if (result.data && result.data.err_cd == "E006" && result.data.data) {
          let errorMsgData = {
            titleErr: "",
            descriptionErr: "",
            priceValueErr: "",
            locationErr: "",
            expiryDateErr: "",
            scheduleTimeErr: "",
            yearOfReleaseErr: "",
            gridTitleErr: "",
          };
          if (result.data.data.title) {
            errorMsgData.titleErr = result.data.data.title;
            errorMsgShow = true;
          }
          if (result.data.data.description) {
            errorMsgData.descriptionErr = result.data.data.description;
            errorMsgShow = true;
          }
          if (result.data.data.price) {
            errorMsgData.priceValueErr = result.data.data.price;
            errorMsgShow = true;
          }
          if (result.data.data.location) {
            errorMsgData.locationErr = result.data.data.location;
            errorMsgShow = true;
          }
          if (result.data.data.expiry_date) {
            errorMsgData.expiryDateErr = result.data.data.expiry_date;
            errorMsgShow = true;
          }
          if (result.data.data.schedule_on) {
            errorMsgData.scheduleTimeErr = result.data.data.schedule_on;
            errorMsgShow = true;
          }
          if (result.data.data.release_year) {
            errorMsgData.yearOfReleaseErr = result.data.data.release_year;
            errorMsgShow = true;
          }
          if (result.data.data.grid_title) {
            errorMsgData.gridTitleErr = result.data.data.release_year;
            errorMsgShow = true;
          }
          if (result.data.data.is_paid) {
            dispatch(
              openSnackBar({
                open: true,
                message: result.data.data.is_paid,
                type: "FAILED",
              })
            );
            errorMsgShow = true;
          }
          setInputValues((prevState) => ({
            ...prevState,
            ...errorMsgData,
          }));
        }
        if (!errorMsgShow) {
          dispatch(
            openSnackBar({
              open: true,
              message: result.message,
              type: "FAILED",
            })
          );
        }
      }
    });
  };
  const handleConfirmationClose = () => {
    setConfirmation((prevState) => ({
      ...prevState,
      open: false,
    }));
  };
  const deleteBtnPress = () => {
    setConfirmation({
      open: true,
      title: "Confirmation",
      message: "Do you want to delete this show?",
      onSubmit: () => {
        callShowDeleteService(state.showSeq);
      },
    });
  };
  const callShowDeleteService = (showSeq) => {
    dispatch(showLoader());
    setShowStatusApi(showSeq, "INACTIVE").then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "SUCCESS",
          })
        );
        navigate(-1);
      } else {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "FAILED",
          })
        );
      }
    });
  };
  const getYearOfReleaseListService = () => {
    dispatch(showLoader());
    getYearOfReleaseListApi().then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        let tempList = [];
        result.data.data.map((item) => {
          tempList.push({ label: item, value: item });
        });
        setYearOfReleaseList(tempList);
      } else {
        setYearOfReleaseList([]);
      }
    });
  };
  return (
    <>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <BackButton
            heading={state.showSeq == -1 ? " Upload New Show" : "Update Show"}
          />
        </Grid>
      </Grid>
      <Grid container spacing={2}>
        <Grid item xs={12} md={8}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <AppInput
                label="Title"
                placeholder="Type here"
                value={inputValues.title}
                onTextChange={handleInputChange("title")}
                maxLength={50}
                errorMessage={inputValues.titleErr}
                marginBottom="18px"
              />

              <AppTextareaInput
                multiline
                minRows={3}
                maxRows={3}
                label="Description"
                placeholder="Type here"
                maxLength={340}
                value={inputValues.description}
                errorMessage={inputValues.descriptionErr}
                onTextChange={handleInputChange("description")}
                marginBottom="18px"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Grid container spacing={2}>
                {/* <Grid item xs={12} md={12} mb={2}>
                                    <AppMediaSelection headings="Upload Playlist Logo"
                                        secondHeading="PNG file, size x size"
                                        selectedMedia={selectedPlaylistLogo}
                                        displayMediaFile={selectedPlaylistLogoFile}
                                        mediaFileName={selectedPlaylistLogoFileName}
                                        onMediaChange={playlistLogoClick}
                                        mediaErr={selectedPlaylistLogoErr} />
                                </Grid>*/}
                <Grid item xs={12} md={12}>
                  <AppInput
                    label="Grid Title"
                    placeholder="Type here"
                    value={inputValues.gridTitle}
                    onTextChange={handleInputChange("gridTitle")}
                    maxLength={24}
                    errorMessage={inputValues.gridTitleErr}
                    marginBottom="18px"
                  />
                </Grid>
                <Grid item xs={12} md={12}>
                  <AppMultiSelectSearch
                    label="Topics"
                    placeholder="Please Select"
                    options={topicsList}
                    selected={selectedTopics}
                    onSelectChange={handleTopicSelection}
                    marginBottom="16px"
                  />
                </Grid>
                {/* <Grid item xs={12} md={6}>
                                    <AppSelect label='Language'
                                        placeholder='Please Select'
                                        options={reactionList}
                                        value={inputValues.season} onSelectChange={handleInputChange('season')}
                                        marginBottom='16px' />
                                </Grid> */}
              </Grid>
            </Grid>
            <Grid item xs={12} md={6}>
              <AppMediaSelection
                headings="Upload Thumbnail Image"
                secondHeading="JPG file, 1080 x 1920 px"
                selectedMedia={selectedThumbnailImage}
                onMediaChange={thumbnailImageClick}
                displayMediaFile={selectedThumbnailImageFile}
                mediaFileName={selectedThumbnailImageFileName}
                mediaErr={selectedThumbnailImageErr}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <AppMediaSelection
                headings="Upload Home Screen image"
                secondHeading="JPG file, 1080 x 1920 px"
                selectedMedia={selectedHomeFileImage}
                onMediaChange={homeFileImageClick}
                displayMediaFile={selectedHomeImageFile}
                mediaFileName={selectedHomeImageFileName}
                mediaErr={selectedHomeImageFileErr}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <AppMediaSelection
                headings="Upload Banner image"
                secondHeading="JPG file, 1920 x 1080 px"
                selectedMedia={selectedBannerFileImage}
                onMediaChange={bannerImageClick}
                displayMediaFile={selectedBannerImageFile}
                mediaFileName={selectedBannerImageFileName}
                mediaErr={selectedBannerImageFileErr}
              />
            </Grid>
            {/*<Grid item xs={12} md={6}>
                            <AppMediaSelection headings="Upload Master Banner"
                                secondHeading="JPG file, size x size"
                                selectedMedia={selectedMasterBannerImage}
                                onMediaChange={masterBannerImageClick}
                                displayMediaFile={selectedMasterBannerImageFile}
                                mediaFileName={selectedMasterBannerImageFileName}
                                mediaErr={selectedMasterBannerImageErr} />
                        </Grid>*/}
            <Grid item xs={12} md={6}>
              {/* <AppMediaSelection headings="Upload Playlist Preview"
                                secondHeading="JPG file, size x size"
                                selectedMedia={selectedPlaylistPreview}
                                onMediaChange={playlistPreviewClick}
                                displayMediaFile={selectedPlaylistPreviewFile}
                                mediaFileName={selectedPlaylistPreviewFileName}
                                mediaErr={selectedPlaylistPreviewErr} /> */}
            </Grid>
            {/* <Grid item xs={12} md={12} mt={2}>
                            <div className='d-flex align-items-center'>
                                <div className='monetizeText me-3'>Monetize</div>
                                {
                                    state.showSeq == -1 ?
                                        <AppSwitch checked={isMonetize} onChange={() => handleMonetize()} />
                                        : null
                                }

                            </div>
                        </Grid> */}
            <Grid item xs={12} mt={2}>
              <Grid container spacing={2}>
                {/* <Grid item xs={12} md={3}>
                                    <AppInput
                                        label='Price'
                                        placeholder='Type here'
                                        value={inputValues.priceValue}
                                        errorMessage={inputValues.priceValueErr}
                                        onTextChange={handleInputChange('priceValue')}
                                        marginBottom='18px'
                                        disabled={!isMonetize}
                                        startIcon={<CurrencyRupeeRoundedIcon fontSize='small' />} />
                                </Grid> */}
                <Grid item xs={12} md={3}>
                  <AppSelect
                    label="Year of Release"
                    placeholder="Please Select"
                    options={yearOfReleaseList}
                    errorMessage={inputValues.yearOfReleaseErr}
                    value={inputValues.yearOfRelease}
                    onSelectChange={handleInputChange("yearOfRelease")}
                    marginBottom="18px"
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <AppSelect
                    label="Location"
                    placeholder="Please Select"
                    options={locationList}
                    errorMessage={inputValues.locationErr}
                    value={inputValues.location}
                    onSelectChange={handleInputChange("location")}
                    marginBottom="18px"
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <AppDatePicker
                    label="Expiry Date"
                    placeholder="Type here"
                    marginBottom="18px"
                    value={inputValues.expiryDate}
                    errorMessage={inputValues.expiryDateErr}
                    onDateChange={handleInputChange("expiryDate")}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <AppDateTimePicker
                    label="Schedule"
                    placeholder="Type here"
                    marginBottom="18px"
                    value={inputValues.scheduleTime}
                    errorMessage={inputValues.scheduleTimeErr}
                    onDateChange={handleInputChange("scheduleTime")}
                  />
                </Grid>
                <Grid item xs={12}></Grid>

                {state.showSeq == -1 ? null : (
                  // <AppOutlineButton label='Preview' onClick={() => console.log("Preview Press")} />
                  <Grid item xs={12} md={4} mb={3}>
                    <AppOutlineButton
                      label="Delete"
                      onClick={() => deleteBtnPress()}
                    />
                  </Grid>
                )}

                <Grid item xs={12} md={8} mb={3}>
                  <AppButton
                    label={`${state.showSeq == -1 ? "Save" : "Update"}`}
                    onClick={() => saveBtnPress()}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={4}>
          <UploadGuideComponent />
          <div className="mt-5">
            <LastSubmittedComponent data={lastSubmittedData} />
          </div>
        </Grid>
      </Grid>
      <AppConfirmation
        open={confirmation.open}
        onClose={handleConfirmationClose}
        onSubmit={confirmation.onSubmit}
        title={confirmation.title}
        message={confirmation.message}
      />
    </>
  );
};

export default AddShowScreen;
