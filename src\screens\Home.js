import React from 'react'
import Error404 from './Error404'
import OverviewScreen from './homeContents/OverviewScreen'
import { useNavigate, useParams } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { Al<PERSON>, Box, Slide, Snackbar } from '@mui/material'
import AppDrawer from '../components/AppDrawer'
import AppHeader from '../components/AppHeader'
import { styled, useTheme } from '@mui/material/styles';
import AppConstants from '../data/AppConstants'
import ContentScreen from './homeContents/ContentScreen'
import AccountScreen from './homeContents/AccountScreen'
import SettingScreen from './homeContents/SettingScreen'
import ContentDetailsScreen from './homeContents/ContentDetailsScreen'
import AddEpisodeScreen from './homeContents/AddEpisodeScreen'
import AddShowScreen from './homeContents/AddShowScreen'
import AddSeasonScreen from './homeContents/AddSeasonScreen'


const pageMap = {
    default: <Error404 />,
    overview: <OverviewScreen />,
    content: <ContentScreen />,
    myAccount: <AccountScreen />,
    settings: <SettingScreen />,
    contentDetails: <ContentDetailsScreen />,
    addEpisode: <AddEpisodeScreen />,
    addShow: <AddShowScreen />,
    addSeason: <AddSeasonScreen />,
    "404": <Error404 />
}
const Home = () => {
    let { pageId } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const open = useSelector((state) => state.drawer.value.open);

    return (
        <>
            <Box sx={{ display: 'flex' }}>
                <AppDrawer />
                <AppHeader />
                <Main open={open}>
                    <Content pageId={pageId} />
                </Main>
            </Box>
        </>
    )
}
const Content = ({ pageId }) => {
    // console.log("pageId", pageId)
    if (pageId == undefined) {
        pageId = "default";
    }
    if (pageMap[pageId] == undefined) {
        pageId = "404";
    }
    return (
        pageMap[pageId]
    );
};
const Main = styled('main', { shouldForwardProp: (prop) => prop !== 'open' })(({ theme, open }) => ({
    ...theme.typography.mainContent,
    ...(!open && {
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
        transition: theme.transitions.create('margin', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen
        }),
        marginTop: "54px",
        width: `calc(100% - 52px))`,
        border: 1,
        borderColor: 'red',
        padding: "20px",
        [theme.breakpoints.up('md')]: {
            marginLeft: `calc(1px)`,
            width: `calc(100% - 52px)`
        },
        [theme.breakpoints.down('md')]: {
            width: `100%`,
        },
    }),
    ...(open && {
        transition: theme.transitions.create('margin', {
            easing: theme.transitions.easing.easeOut,
            duration: theme.transitions.duration.enteringScreen
        }),
        padding: "20px",
        marginTop: "54px",
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
        [theme.breakpoints.up('md')]: {
            width: `calc(100% - ${AppConstants.DRAWER_WIDTH}px)`
        },
        [theme.breakpoints.down('md')]: {
            width: '100%',
        }
    })
}));


export default Home