import React, { useState } from 'react'

const LastSubmittedComponent = ({ data = [] }) => {
    const LastSubmittedItem = ({
        item
    }) => {
        return (<div className='d-flex mb-3'>
            <div className='d-flex flex-column'>
                <div className='submittedTitle'>{item.title}</div>
                <div className='submittedDate'>{item.time}</div>
            </div>
            <div className='ms-auto'>
                <div className='submittedStatus text-center'>
                    {item.type}
                </div>

            </div>

        </div>);
    }
    return (
        <>
            <div className='formBoxTitle'>Last Submitted</div>
            <div className='mt-2'>
                {
                    data.map((obj, i) => {
                        return <LastSubmittedItem key={i} item={obj} />
                    })
                }
            </div>
        </>
    )
}

export default LastSubmittedComponent