import { Grid } from '@mui/material'
import React, { useState } from 'react'
import { AppInput } from '../../components/ui_component/AppInput';
import { AppButton } from '../../components/ui_component/AppButton';

const SettingScreen = () => {
    const [inputValues, setInputValues] = useState({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
        currentPasswordErr: "",
        newPasswordErr: "",
        confirmPasswordErr: ""

    });

    const handleInputChange = (name) => (value) => {
        setInputValues(prevState => ({
            ...prevState, [name]: value, [name + "Err"]: ""
        }));
    }
    const updatePasswordBtn = () => {
        // console.log("updatePasswordBtn")

    }
    return (
        <>
            <Grid container>
                <Grid item xs={12} className='mb-3'>
                    <div className='pageTitle pageTitleGap'>Settings</div>
                </Grid>
            </Grid>
            <Grid container spacing={2}>
                <Grid item xs={12}>
                    <div className='formBoxTitle'>Update Password</div>
                </Grid>
                <Grid item xs={12} md={4}>
                    <AppInput
                        label='Current Password'
                        placeholder='Type here'
                        value={inputValues.currentPassword}
                        errorMessage={inputValues.currentPasswordErr}
                        onTextChange={handleInputChange('currentPassword')}
                        marginBottom='16px' />
                </Grid>
                <Grid item xs={12} md={4}>
                    <AppInput
                        label='New Password'
                        placeholder='Type here'
                        value={inputValues.newPassword}
                        errorMessage={inputValues.newPasswordErr}
                        onTextChange={handleInputChange('newPassword')}
                        marginBottom='16px' />
                </Grid>
                <Grid item xs={12} md={4}>
                    <AppInput
                        label='Confirm Password'
                        placeholder='Type here'
                        value={inputValues.confirmPassword}
                        errorMessage={inputValues.confirmPasswordErr}
                        onTextChange={handleInputChange('confirmPassword')}
                        marginBottom='16px' />
                </Grid>

                <Grid item xs={12} md={2}>
                    <AppButton label='Update' onClick={() => updatePasswordBtn()} />
                </Grid>

            </Grid>
        </>
    )
}

export default SettingScreen