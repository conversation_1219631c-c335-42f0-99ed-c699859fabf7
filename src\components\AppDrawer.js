import { Box, Drawer, Typography, useMediaQuery } from '@mui/material'
import React, { useEffect } from 'react'
import { useTheme } from '@mui/material/styles';
import AppConstants from '../data/AppConstants';
import AppLogo from '../assets/images/logo.png'
import { useDispatch, useSelector } from 'react-redux';
import { close as drawerClose, setSelectedMenuId } from '../state/slice/drawerSlice'
import Person2Icon from '@mui/icons-material/Person2';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import AppRoutes from '../data/AppRoutes';
import { removeAccessKey, removeAppDataLocal, removeUserCredential } from '../storage/auth.login';
import AppConfirmation from './ui_component/AppConfirmation';
import { logout } from '../state/slice/loginSlice';
import { AppPageIDMapping } from '../data/AppPageIDMapping';
import OVERVIEW_ICON from '../assets/images/svgs/Dashboard_Overview.svg'
import CONTENT_ICON from '../assets/images/svgs/Content.svg'
import LOGOUT_ICON from '../assets/images/icons/logout.png'


const AppDrawer = (props) => {
    const { window } = props;
    const dispatch = useDispatch();
    const navigate = useNavigate();
    let { pageId } = useParams();
    useEffect(() => {
        if (pageId != undefined) {
            const selectedPageId = AppPageIDMapping[pageId]
            if (selectedPageId) {
                dispatch(setSelectedMenuId(selectedPageId));
            }
        }
    }, []);
    const theme = useTheme();
    const matchUpMd = useMediaQuery(theme.breakpoints.up('md'));
    const container = window !== undefined ? () => window.document.body : undefined;
    const selectedMenu = useSelector((state) => state.drawer.value.selectedMenuId);
    const open = useSelector((state) => state.drawer.value.open);
    const [confirmation, setConfirmation] = React.useState({
        open: false,
        title: "",
        message: "",
        onSubmit: null
    });
    const DrawerItemIcon = ({ icon = null, image = null, customComponent = null }) => {
        if (icon != null) {
            return icon;
        }
        else if (image != null) {
            return <img src={image} alt="icon" style={{ width: 28, height: 28, objectFit: 'contain' }} />;
        }
        else if (customComponent != null) {
            return customComponent;
        }
    }
    const LogoutIcon = () => {
        return <img src={LOGOUT_ICON} alt="icon" style={{ width: 18, height: 18 }} />

    }
    const menuItems = [
        {
            id: "menu_overview",
            type: "MAIN",
            label: "Overview",
            icon: <DrawerItemIcon image={OVERVIEW_ICON} />,
            show_menu: true,
            action: () => {
                navigate(AppRoutes.OVERVIEW_PATH, { replace: true });
            },
            children: []
        },
        // {
        //     id: "menu_account_details",
        //     type: "MAIN",
        //     label: "Account Details",
        //     icon: <DrawerItemIcon icon={<Person2Icon />} />,
        //     show_menu: true,
        //     action: () => {
        //         navigate(AppRoutes.MY_ACCOUNT_PATH, { replace: true });
        //     },
        //     children: []
        // },
        {
            id: "menu_content",
            type: "MAIN",
            label: "Content",
            icon: <DrawerItemIcon image={CONTENT_ICON} />,
            show_menu: true,
            action: () => {
                navigate(AppRoutes.CONTENT_PATH, { replace: true });
            },
            children: []
        },
        {
            id: "menu_footer_logout",
            type: "FOOTER",
            label: "Logout",
            icon: <DrawerItemIcon customComponent={<LogoutIcon />} />,
            show_menu: true,
            action: () => {
                appLogout()
            },
            children: []
        },
        // {
        //     id: "menu_footer_help_center",
        //     type: "FOOTER",
        //     label: "Help Center",
        //     icon: <DrawerItemIcon icon={<Person2Icon />} />,
        //     show_menu: true,
        //     action: () => {
        //         console.log("menu_footer_help_center")
        //     },
        //     children: []
        // },
        // {
        //     id: "menu_footer_settings",
        //     type: "FOOTER",
        //     label: "Settings",
        //     icon: <DrawerItemIcon icon={<Person2Icon />} />,
        //     show_menu: true,
        //     action: () => {
        //         navigate(AppRoutes.SETTINGS_PATH, { replace: true });
        //     },
        //     children: []
        // },
    ]
    const appLogout = () => {

        setConfirmation({
            open: true,
            title: "Confirmation",
            message: "Do you want to Logout?",
            onSubmit: () => {
                dispatch(logout());
                removeUserCredential();
                removeAccessKey();
                removeAppDataLocal();
                navigate.index = 0;
                navigate(AppRoutes.LOGIN_PATH, { replace: true })
            }
        });

    }
    const handleDrawerClose = () => {
        dispatch(drawerClose());
    };
    const handleMenuClick = (id, action) => {
        if (!["menu_footer_logout"].includes(id)) {
            dispatch(setSelectedMenuId(id));
        }

        if (action) {
            action();
        }
        if (!matchUpMd) {
            handleDrawerClose();
        }
    };

    const DrawerContent = () => {
        return (
            <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', padding: "15px 24px", }}>
                <div className='text-left'>
                    <img src={AppLogo} className='drawerLogo' alt='logo' />
                </div>

                <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
                    {
                        menuItems.map((item, index) => {
                            if (item.type != "FOOTER") {
                                return <DrawerMenuItem
                                    key={item.id}
                                    selected={selectedMenu == item.id}
                                    selectedMenu={selectedMenu}
                                    id={item.id}
                                    open={open}
                                    matchUpMd={matchUpMd}
                                    label={item.label}
                                    icon={item.icon}
                                    action={item.action}
                                    item={item}
                                    menuClicked={handleMenuClick} />
                            }

                        })
                    }
                </Box>
                <Box> {/* footer */}
                    {
                        menuItems.map((item, index) => {
                            if (item.type == "FOOTER") {
                                return <DrawerFooterItem
                                    key={item.id}
                                    selected={selectedMenu == item.id}
                                    selectedMenu={selectedMenu}
                                    id={item.id}
                                    open={open}
                                    matchUpMd={matchUpMd}
                                    action={item.action}
                                    item={item}
                                    menuClicked={handleMenuClick} />
                            }

                        })
                    }
                </Box>
            </Box>
        )
    }

    const DrawerMenuItem = ({ item, showMenu = true, selected = false, ...props }) => {
        const handleMenuClick = (id, action) => {
            props.menuClicked(id, action);
        }
        return (
            <div className={`drawerMenuItem d-flex align-items-center ${selected ? "active" : ""}`} onClick={() => handleMenuClick(item.id, item.action)}>
                <div className='drawerMenuItemIcon'>{item.icon}</div>
                <div className='drawerMenuItemLabel'>{item.label}</div>
            </div>
        )
    }
    const DrawerFooterItem = ({ item, showMenu = true, selected = false, ...props }) => {
        const handleMenuClick = (id, action) => {
            props.menuClicked(id, action);
        }
        return (
            <div className={`drawerMenuItem d-flex align-items-center ${selected ? "active" : ""}`} onClick={() => handleMenuClick(item.id, item.action)}>
                <div className='drawerMenuItemIcon'>{item.icon}</div>
                <div className='drawerMenuItemLabel'>{item.label}</div>
            </div>
        )

    }
    const handleConfirmationClose = () => {
        setConfirmation(prevState => ({
            ...prevState, open: false
        }));
    }
    return (
        <Box component="nav"
            sx={{
                transition: theme.transitions.create(['width', 'margin'], {
                    easing: theme.transitions.easing.sharp,
                    duration: theme.transitions.duration.enteringScreen,
                }),
                transition: theme.transitions.create(['width', 'margin'], {
                    easing: theme.transitions.easing.sharp,
                    duration: theme.transitions.duration.leavingScreen,
                }),
                backgroundColor: "#0A0A0C",

                flexShrink: { md: 0 },
                width: AppConstants.DRAWER_WIDTH
            }}>
            <Drawer
                container={container}
                variant={matchUpMd ? 'permanent' : 'temporary'}
                anchor="left"
                open={true}
                onClose={handleDrawerClose}
                sx={{
                    '& .MuiDrawer-paper': {
                        transition: theme.transitions.create(['width', 'margin'], {
                            easing: theme.transitions.easing.sharp,
                            duration: theme.transitions.duration.enteringScreen,
                        }),
                        transition: theme.transitions.create(['width', 'margin'], {
                            easing: theme.transitions.easing.sharp,
                            duration: theme.transitions.duration.leavingScreen,
                        }),
                        width: AppConstants.DRAWER_WIDTH,
                        background: "#0A0A0C",

                    }
                }}
                ModalProps={{ keepMounted: true }}
                color="inherit">
                <DrawerContent />
            </Drawer>
            <AppConfirmation
                open={confirmation.open}
                onClose={handleConfirmationClose}
                onSubmit={confirmation.onSubmit}
                title={confirmation.title}
                message={confirmation.message} />

        </Box>
    )
}

export default AppDrawer