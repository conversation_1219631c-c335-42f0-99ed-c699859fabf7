import {
  Box,
  FormHelperText,
  IconButton,
  InputAdornment,
  TextField,
  Typography,
} from "@mui/material";
import useAppTheme from "../../ui_theme/useAppTheme";
import VisibilityRoundedIcon from "@mui/icons-material/VisibilityRounded";
import VisibilityOffRoundedIcon from "@mui/icons-material/VisibilityOffRounded";
import { useEffect, useState } from "react";

export const AppInput = ({
  label = "",
  placeholder = "",
  onTextChange = null,
  value = "",
  type = "text",
  style = null,
  errorMessage = "",
  maxLength = -1,
  onEnterPressed = null,
  disabled = false,
  disableColor = null,
  autoComplete = "off",
  marginBottom = "1.5rem",
  startIcon = null,
  isPasswordField = false,
  readOnly = false,
  endComponent = null,
  ...props
}) => {
  const appTheme = useAppTheme();
  const onChange = (event) => {
    if (maxLength != -1) {
      if (event.target.value.length > maxLength) return;
    }
    if (onTextChange) onTextChange(event.target.value);
  };
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    setShowPassword(false);
  }, [isPasswordField]);

  const handleKeyDown = (event) => {
    var code = event.keyCode || event.which;

    if (code === 13 && onEnterPressed) onEnterPressed();
  };
  const handleClickShowPassword = () => {
    setShowPassword((prevState) => !prevState);
  };
  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  return (
    <Box
      sx={{
        marginBottom: marginBottom,
      }}
    >
      {label.length != 0 ? (
        <Typography
          sx={{
            color: appTheme.colors.inputLabelColor,
            fontSize: appTheme.dimensions.inputLabelSize,
            marginBottom: "0.3rem",
            marginLeft: "0.2rem",
          }}
        >
          {label}
        </Typography>
      ) : null}

      <TextField
        type={isPasswordField ? (showPassword ? "text" : "password") : type}
        variant="outlined"
        fullWidth
        size="small"
        onChange={onChange}
        value={value}
        placeholder={placeholder}
        style={style}
        error={errorMessage.length > 0}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        InputProps={{
          ...(startIcon != null
            ? {
                startAdornment: (
                  <InputAdornment
                    style={{
                      color: appTheme.colors.inputAdornmentColor,
                      background: disableColor
                        ? disableColor
                        : appTheme.colors.inputBackgroundColor,
                    }}
                    position="start"
                  >
                    {startIcon}
                  </InputAdornment>
                ),
              }
            : null),

          ...(isPasswordField
            ? {
                endAdornment: (
                  <InputAdornment
                    style={{
                      color: appTheme.colors.inputAdornmentColor,
                      background: disableColor
                        ? disableColor
                        : appTheme.colors.inputBackgroundColor,
                    }}
                    position="end"
                  >
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleClickShowPassword}
                      onMouseDown={handleMouseDownPassword}
                      edge="end"
                    >
                      {showPassword ? (
                        <VisibilityRoundedIcon />
                      ) : (
                        <VisibilityOffRoundedIcon />
                      )}
                    </IconButton>
                  </InputAdornment>
                ),
              }
            : {}),
          ...(endComponent != null
            ? {
                endAdornment: (
                  <InputAdornment
                    style={{
                      color: appTheme.colors.inputAdornmentColor,
                      background: disableColor
                        ? disableColor
                        : appTheme.colors.inputBackgroundColor,
                    }}
                    position="end"
                  >
                    {endComponent}
                  </InputAdornment>
                ),
              }
            : null),
          readOnly: readOnly,
        }}
        sx={{
          "& legend": { display: "none" },
          borderRadius: appTheme.dimensions.inputRadius + "px",
          background: disableColor
            ? disableColor
            : appTheme.colors.inputBackgroundColor,
          input: {
            background: disableColor
              ? disableColor
              : appTheme.colors.inputBackgroundColor,
            color: appTheme.colors.inputColor,

            marginTop: 0,
            "&::placeholder": {
              color: appTheme.colors.inputPlaceholderColor,
              fontSize: appTheme.dimensions.defaultFontSize,
            },
            "&:disabled": {
              // color: disableColor,
              // "-webkit-text-fill-color": disableColor
            },
            borderRadius: appTheme.dimensions.inputRadius + "px",
          },
          "& .MuiOutlinedInput-root": {
            borderRadius: appTheme.dimensions.inputRadius + "px",
          },
          "& .MuiOutlinedInput-notchedOutline": {
            top: "0px",
            borderRadius: appTheme.dimensions.inputRadius + "px",
          },
          "& .Mui-focused .MuiOutlinedInput-notchedOutline": {
            top: "0px",
            borderRadius: appTheme.dimensions.inputRadius + "px",
          },
        }}
        autoComplete={autoComplete}
        {...props}
      />
      <FormHelperText error={true}>{errorMessage}</FormHelperText>
    </Box>
  );
};
export const AppTextareaInput = ({
  label = "",
  placeholder = "",
  onTextChange = null,
  value = "",
  type = "text",
  style = null,
  errorMessage = "",
  maxLength = -1,
  onEnterPressed = null,
  disabled = false,
  disableColor = null,
  autoComplete = "off",
  marginBottom = "1.5rem",

  ...props
}) => {
  const appTheme = useAppTheme();
  const onChange = (event) => {
    if (maxLength != -1) {
      if (event.target.value.length > maxLength) return;
    }
    if (onTextChange) onTextChange(event.target.value);
  };

  const handleKeyDown = (event) => {
    var code = event.keyCode || event.which;

    if (code === 13 && onEnterPressed) onEnterPressed();
  };
  return (
    <Box
      sx={{
        marginBottom: marginBottom,
      }}
    >
      {label.length != 0 ? (
        <Typography
          sx={{
            color: appTheme.colors.inputLabelColor,
            fontSize: appTheme.dimensions.inputLabelSize,
            marginBottom: "0.3rem",
            marginLeft: "0.2rem",
          }}
        >
          {label}
        </Typography>
      ) : null}

      <TextField
        type={type}
        variant="outlined"
        fullWidth
        size="small"
        onChange={onChange}
        value={value}
        placeholder={placeholder}
        style={style}
        error={errorMessage.length > 0}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        multiline
        sx={{
          "& legend": { display: "none" },
          background: appTheme.colors.inputBackgroundColor,
          borderRadius: appTheme.dimensions.inputRadius + "px",
          textArea: {
            background: appTheme.colors.inputBackgroundColor,
            color: appTheme.colors.inputColor,
            marginTop: 0,
            "&::placeholder": {
              color: appTheme.colors.inputPlaceholderColor,
              fontSize: appTheme.dimensions.defaultFontSize,
            },
            "&:disabled": {
              color: disableColor,
              "-webkit-text-fill-color": disableColor,
            },
            borderRadius: appTheme.dimensions.inputRadius + "px",
          },
          "& .MuiOutlinedInput-notchedOutline": {
            top: "0px",
            borderRadius: appTheme.dimensions.inputRadius + "px",
          },
          "& .Mui-focused .MuiOutlinedInput-notchedOutline": {
            top: "0px",
            borderRadius: appTheme.dimensions.inputRadius + "px",
          },
        }}
        autoComplete={autoComplete}
        {...props}
      />
      <FormHelperText error={true}>{errorMessage}</FormHelperText>
    </Box>
  );
};
