import { createSlice } from '@reduxjs/toolkit'

export const loaderSlice = createSlice({
    name: 'drawerSlice',
    initialState: {
        value: {
            open: true,
        },
    },
    reducers: {
        showLoader: (state) => {
            state.value.open = true
        },
        closeLoader: (state) => {
            state.value.open = false
        },
    },
});

// Action creators are generated for each case reducer function
export const { showLoader, closeLoader } = loaderSlice.actions

export default loaderSlice.reducer