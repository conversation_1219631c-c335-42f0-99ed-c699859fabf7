import { Grid } from '@mui/material'
import React, { useState } from 'react'
import { AppInput } from '../../components/ui_component/AppInput'
import { AppSelect } from '../../components/ui_component/AppSelect';
import AppMediaSelection from '../../components/ui_component/AppMediaSelection';
import { AppButton } from '../../components/ui_component/AppButton';
import UserPlaceholder from '../../assets/images/default_image.jpg'

const AccountScreen = () => {
    const [inputValues, setInputValues] = useState({
        name: '',
        bio: '',
        website: '',
        selectedTopic: '',
        bankName: "",
        accountNumber: "",
        confirmAccountNumber: "",
        ifscCode: "",

        nameErr: '',
        bioErr: '',
        websiteErr: '',
        selectedTopicErr: '',
        bankNameErr: "",
        accountNumberErr: "",
        confirmAccountNumberErr: "",
        ifscCodeErr: "",

    });
    const handleInputChange = (name) => (value) => {
        setInputValues(prevState => ({
            ...prevState, [name]: value, [name + "Err"]: ""
        }));
    }
    const [topicData, setTopicData] = useState([
        { value: 1, label: 'React' },
        { value: 2, label: 'Angular' },
        { value: 3, label: 'Vue' },


    ])
    const submitVerificationBtn = () => {
        // console.log("submitVerificationBtn")
    }
    const UserCountBox = ({
        label = "", value = ""
    }) => {
        return <div className='userCountBoxInnerItem d-flex flex-column text-center'>
            <span className='userCountBoxInnerItem__value'>{value}</span>
            <span className='userCountBoxInnerItem__label'>{label}</span>
        </div>
    }
    const [accountType, setAccountType] = useState("UPI");
    const handleAccountType = (selectedValue) => {
        setAccountType(selectedValue)
    }

    return (
        <>
            <Grid container>
                <Grid item xs={12} md={4} className='pe-4'>
                    <div className='userImageBox '>
                        <img src={UserPlaceholder} alt="user" className='img-fluid userImageBox' />
                        <div className='userGradient' />
                        <div className='userCountBox'>
                            <div className='userCountBoxInner d-flex w-100 align-items-center 
                            justify-content-evenly'>
                                <UserCountBox label='Post' value='26' />
                                <UserCountBox label='Fans' value='58' />
                                <UserCountBox label='Following' value='369' />
                            </div>

                        </div>
                    </div>
                </Grid>
                <Grid item xs={12} md={8}>
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                            <AppInput
                                label='Name'
                                placeholder='Type here'
                                value={inputValues.name}
                                errorMessage={inputValues.nameErr}
                                onTextChange={handleInputChange('name')}
                                marginBottom='16px' />
                            <AppInput
                                label='Bio'
                                placeholder='Type here'
                                value={inputValues.bio}
                                errorMessage={inputValues.bioErr}
                                onTextChange={handleInputChange('bio')}
                                marginBottom='16px' />
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <div className='d-flex align-items-center justify-content-center h-100'>
                                <AppMediaSelection headings="Upload Profile Picture"
                                    secondHeading="PNG file, size x size" />
                            </div>

                        </Grid>
                        <Grid item xs={12} md={6}>
                            <AppInput
                                label='Website'
                                placeholder='Type here'
                                value={inputValues.website}
                                errorMessage={inputValues.websiteErr}
                                onTextChange={handleInputChange('website')}
                                marginBottom='16px' />
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <AppSelect label='Topics' placeholder='Please Select' options={topicData}
                                value={inputValues.selectedTopic} onSelectChange={handleInputChange('selectedTopic')}
                                marginBottom='16px' />
                        </Grid>
                        <Grid item xs={12} md={6} className='mt-1'>
                            <AppMediaSelection headings="Upload Aadhar Card (Front)"
                                secondHeading="PDF, JPEG, PNG" />
                        </Grid>
                        <Grid item xs={12} md={6} className='mt-1'>
                            <AppMediaSelection headings="Upload Aadhar Card (Back)"
                                secondHeading="PDF, JPEG, PNG" />
                        </Grid>
                        <Grid item xs={12} md={6} className='mt-2'>
                            <AppMediaSelection headings="Upload PAN Card"
                                secondHeading="PDF, JPEG, PNG" />
                        </Grid>
                        <Grid item xs={12} md={12} className='mt-3'>
                            <AppButton label='Submit for Verification' onClick={() => submitVerificationBtn()} />
                        </Grid>
                    </Grid>

                </Grid>
            </Grid>
            <Grid container mt={3}>
                <Grid item xs={12}>
                    <div className='formBoxTitle'>Bank Account Details</div>
                </Grid>
                <Grid item>
                    <div className='formTabBox d-flex align-items-center'>
                        <div className={`formTabBox__item ${accountType == "UPI" ? "active" : ""}`} onClick={() => handleAccountType("UPI")}>UPI</div>
                        <div className={`formTabBox__item ${accountType == "NON_UPI" ? "active" : ""}`} onClick={() => handleAccountType("NON_UPI")}>Non UPI</div>
                    </div>
                </Grid>
            </Grid>
            {
                accountType == "NON_UPI" ?
                    <Grid container mt={2} spacing={2} >
                        <Grid item xs={12} md={4} >
                            <AppInput
                                label='Bank'
                                placeholder='Type here'
                                value={inputValues.bankName}
                                errorMessage={inputValues.bankNameErr}
                                onTextChange={handleInputChange('bankName')}
                                marginBottom='16px' />
                        </Grid>
                        <Grid item xs={12} md={4}>
                            <AppInput
                                label='Account Number'
                                placeholder='Type here'
                                value={inputValues.accountNumber}
                                errorMessage={inputValues.accountNumberErr}
                                onTextChange={handleInputChange('accountNumber')}
                                marginBottom='16px' />
                        </Grid>
                        <Grid item xs={12} md={4}>
                            <AppInput
                                label='Confirm Account Number'
                                placeholder='Type here'
                                value={inputValues.confirmAccountNumber}
                                errorMessage={inputValues.confirmAccountNumberErr}
                                onTextChange={handleInputChange('confirmAccountNumber')}
                                marginBottom='16px' />
                        </Grid>
                        <Grid item xs={12} md={4}>
                            <AppInput
                                label='IFSC Code'
                                placeholder='Type here'
                                value={inputValues.ifscCode}
                                errorMessage={inputValues.ifscCodeErr}
                                onTextChange={handleInputChange('ifscCode')}
                                marginBottom='16px' />
                        </Grid>
                        <Grid item xs={12} />
                        <Grid item xs={12} md={8} mb={4}>
                            <AppButton label='Submit for review' onClick={() => submitVerificationBtn()} />
                        </Grid>

                    </Grid>
                    : null
            }
            {
                accountType == "UPI" ?
                    <Grid container mt={2} spacing={2}>
                        <Grid item xs={12} md={4} >
                            <AppInput
                                label='IFSC Code'
                                placeholder='Type here'
                                value={inputValues.ifscCode}
                                errorMessage={inputValues.ifscCodeErr}
                                onTextChange={handleInputChange('ifscCode')}
                                marginBottom='16px' />
                        </Grid>
                        <Grid item xs={12} md={2} mb={4} >
                            <div className='sideFormButtonGap'>
                                <AppButton label='Verify' onClick={() => submitVerificationBtn()} />
                            </div>

                        </Grid>
                    </Grid>
                    : null}
        </>
    )
}

export default AccountScreen