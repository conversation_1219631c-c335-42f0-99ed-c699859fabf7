import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import AppLoader from "../../components/ui_component/AppLoader";
import OverviewCountCard from "../../components/overviewchart/OverviewCountCard";
import UserPlaceHolderImage from "../../assets/images/user_image_place_holder.png";
import ImagePlaceholder from "../../assets/images/default_image.jpg";
import { Box, Grid, Paper } from "@mui/material";
import MoreHorizRoundedIcon from "@mui/icons-material/MoreHorizRounded";
import AudienceChart from "../../components/overviewchart/AudienceChart";
import EditRoundedIcon from "@mui/icons-material/EditRounded";
import DeleteOutlineRoundedIcon from "@mui/icons-material/DeleteOutlineRounded";
import PlaylistItem from "../../components/contentscreen/PlaylistItem";
import { AppSelect } from "../../components/ui_component/AppSelect";
import { checkValueLength } from "../../util/StringUtils";
import { useDispatch } from "react-redux";
import { closeLoader, showLoader } from "../../state/slice/loaderSlice";
import {
  getAllEpisodeApi,
  getAllSeasonsApi,
  getSeasonListApi,
  getShowDetailsApi,
  setEpisodeStatusApi,
  setShowStatusApi,
} from "../../api/Apis";
import AppRoutes from "../../data/AppRoutes";
import { openSnackBar } from "../../state/slice/snackbarSlice";
import AppConfirmation from "../../components/ui_component/AppConfirmation";
import VisibleErrorMessage from "../../components/ui_component/VisibleErrorMessage";
import BackButton from "../../components/common/BackButton";
import EDIT_ICON from "../../assets/images/svgs/Edit.svg";
import DELETE_ICON from "../../assets/images/svgs/Delete.svg";
const ContentDetailsScreen = () => {
  const [loading, setLoading] = useState(false);
  const { state } = useLocation();

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [showTitle, setShowTitle] = useState("");
  const [showDescription, setShowDescription] = useState("");
  const [showLogo, setShowLogo] = useState(null);
  const [confirmation, setConfirmation] = React.useState({
    open: false,
    title: "",
    message: "",
    onSubmit: null,
  });
  const [episodeList, setEpisodeList] = useState([]);
  const [episodeDataErrorMessage, setEpisodeDataErrorMessage] = useState("");

  useEffect(() => {
    callShowDetailsService();
  }, []);
  console.log("hi");
  const callShowDetailsService = () => {
    setLoading(true);
    if (checkValueLength(state.showSeq) && state.showSeq != -1) {
      showSeasonService(state.showSeq);
      seasonEpisodesService(state.showSeq, -1);

      dispatch(showLoader());
      getShowDetailsApi(state.showSeq).then((result) => {
        setLoading(false);
        if (result.isSuccess) {
          const showData = result.data.data[0];
          setShowTitle(showData.title);
          setShowDescription(showData.description);
          setShowLogo(showData.thumb_file);
        } else {
          dispatch(
            openSnackBar({
              open: true,
              message: result.message,
              type: "FAILED",
            })
          );
          // navigate(-1)
        }
      });
    } else {
      navigate(-1);
    }
  };
  const [seasonList, setSeasonList] = useState([]);
  const [selectedSeason, setSelectedSeason] = useState("");

  const showSeasonService = (showSeq) => {
    setLoading(true);
    dispatch(showLoader());
    getSeasonListApi(showSeq).then((result) => {
      dispatch(closeLoader());
      setLoading(false);
      if (result.isSuccess) {
        let tempList = [];
        result.data.data.map((obj) => {
          tempList.push({ label: obj.title, value: obj.season_seq });
        });
        setSeasonList(tempList);
      } else {
        setSeasonList([]);
      }
    });
  };

  const EpisodeItemRow = ({ item, index, rowBtnClick = null }) => {
    return (
      <div className="d-flex showClipItem mb-3">
        <div className="">
          <img
            src={item.thumb_file}
            alt="user"
            className="showClipItem__image"
          />
        </div>
        <div className="showClipItem__content d-flex flex-column align-items-start w-100">
          <div className="d-flex">
            <div className="">Episode {index + 1}</div>
            <div className=" ms-5">
              {/* <EditRoundedIcon className='showClipItem__moreIcon'
                                onClick={() => rowBtnClick("EDIT", { index: index })} /> */}
              <img
                src={EDIT_ICON}
                className="listRowIcon"
                alt="icon"
                onClick={() => rowBtnClick("EDIT", { index: index })}
              />
              <img
                src={DELETE_ICON}
                className="listRowIcon"
                alt="icon"
                onClick={() => rowBtnClick("DELETE", { index: index })}
              />
            </div>
          </div>

          <div className="showClipItem__content--title">{item.title}</div>
          {/* <div className='mt-2'>
                        <div className='d-flex '>
                            <div className='showClipItem__content--text value'>1.4M</div>
                            <div className='showClipItem__content--text'>Views</div>
                        </div>
                        <div className='d-flex '>
                            <div className='showClipItem__content--text value'>244</div>
                            <div className='showClipItem__content--text'>Reactions</div>
                        </div>
                        <div className='d-flex '>
                            <div className='showClipItem__content--text value'>24</div>
                            <div className='showClipItem__content--text'>Comments</div>
                        </div>
                    </div> */}
        </div>
      </div>
    );
  };
  const playlistItemClick = (idVal) => {
    // Navigate to episode details if idVal exists
    if (idVal) {
      navigate(AppRoutes.ADD_EPISODE_PATH, { state: { episodeSeq: idVal } });
    }
  };
  const handleSeasonSelection = (item) => {
    setSelectedSeason(item);
    seasonEpisodesService(state.showSeq, item);
  };
  const handleConfirmationClose = () => {
    setConfirmation((prevState) => ({
      ...prevState,
      open: false,
    }));
  };
  const deleteBtnPress = () => {
    setConfirmation({
      open: true,
      title: "Confirmation",
      message: "Do you want to delete this show?",
      onSubmit: () => {
        callShowDeleteService(state.showSeq);
      },
    });
  };
  const callShowDeleteService = (showSeq) => {
    dispatch(showLoader());
    setShowStatusApi(showSeq, "INACTIVE").then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "SUCCESS",
          })
        );
        navigate(-1);
      } else {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "FAILED",
          })
        );
      }
    });
  };
  const updateShowBtnPress = () => {
    navigate(AppRoutes.ADD_SHOW_PATH, { state: { showSeq: state.showSeq } });
  };
  const seasonEpisodesService = (showSeq, seasonSeq) => {
    setLoading(true);
    dispatch(showLoader());
    getAllEpisodeApi(showSeq, seasonSeq).then((result) => {
      dispatch(closeLoader());
      setLoading(false);
      if (result.isSuccess) {
        setEpisodeDataErrorMessage("");
        setEpisodeList(result.data.data);
      } else {
        setEpisodeList([]);
        setEpisodeDataErrorMessage(result.message);
      }
    });
  };
  const episodeItemClick = (clickID, obj) => {
    if (clickID == "DELETE") {
      const dataObj = episodeList[obj.index];
      setConfirmation({
        open: true,
        title: "Confirmation",
        message: "Do you want to delete this episode?",
        onSubmit: () => {
          callEpisodeDeleteService(dataObj.episode_seq);
        },
      });
    } else if (clickID == "EDIT") {
      const dataObj = episodeList[obj.index];
      navigate(AppRoutes.ADD_EPISODE_PATH, {
        state: { episodeSeq: dataObj.episode_seq },
      });
    }
  };
  const callEpisodeDeleteService = (episode_seq) => {
    dispatch(showLoader());
    setEpisodeStatusApi(episode_seq, "INACTIVE").then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "SUCCESS",
          })
        );
        seasonEpisodesService(state.showSeq, selectedSeason);
      } else {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "FAILED",
          })
        );
      }
    });
  };
  const ButtonIcon = ({ icon = null }) => {
    return <img src={icon} className="buttonIcon" alt="button icon" />;
  };
  return (
    <>
      {loading && <AppLoader />}
      <Grid container>
        <Grid item xs={12} md={12}>
          <BackButton heading="Show" />
        </Grid>
        <Grid item xs={12} md={7}>
          <div className="showBox d-flex">
            <div className="">
              <img src={showLogo} alt="user" className="showBox__image" />
            </div>
            <div className="showBox__content d-flex flex-column align-items-start">
              <div className="showBox__content--title">{showTitle}</div>
              <div className="showBox__content--desc flex-grow-1">
                {showDescription}
              </div>
              <div
                className="d-flex "
                style={{ width: "200px", marginTop: "1rem" }}
              >
                <Grid item xs={12} md={12}>
                  <AppSelect
                    label=""
                    placeholder="Please Select"
                    options={seasonList}
                    value={selectedSeason}
                    onSelectChange={handleSeasonSelection}
                    marginBottom="8px"
                  />
                </Grid>
              </div>
              <div className="showBox__content--actionbox d-flex">
                <div
                  className="showBox__content--actionbox__button d-flex align-items-center justify-content-center normalBtn me-2"
                  onClick={() => updateShowBtnPress()}
                >
                  <ButtonIcon icon={EDIT_ICON} />
                  <span className="ms-1">Update</span>
                </div>
                <div
                  className="showBox__content--actionbox__button d-flex align-items-center
                                 justify-content-center errorBtn"
                  onClick={() => deleteBtnPress()}
                >
                  <ButtonIcon icon={DELETE_ICON} />
                  <span className="ms-1">Delete</span>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-5" style={{ backgroundColor: "red" }}>
            {episodeList.map((item, index) => {
              return (
                <EpisodeItemRow
                  key={index}
                  index={index}
                  item={item}
                  rowBtnClick={episodeItemClick}
                />
              );
            })}
            {episodeDataErrorMessage.length > 0 ? (
              <VisibleErrorMessage errorMessage={episodeDataErrorMessage} />
            ) : null}
          </div>
        </Grid>
        <Grid item xs={12} md={5}>
          <div className="d-flex justify-content-between cardGapBottom">
            <OverviewCountCard
              heading="User Reach"
              count="3,914"
              percentageType="DOWN"
              icon={UserPlaceHolderImage}
              percentage="+ 0.2%"
              percentageTime="This week"
              className="flex-grow-1"
            />
            <OverviewCountCard
              heading="Account Views"
              count="105,124"
              percentageType="UP"
              icon={UserPlaceHolderImage}
              percentage="+ 4.5%"
              percentageTime="This week"
              className="flex-grow-1 cardGapLeft"
            />
          </div>
          <Paper
            elevation={3}
            className="overviewCard d-flex flex-column cardGapTop "
          >
            <div className="d-flex CardHeader">
              <div className="CardHeader__title">Audience Chart</div>

              <div className="ms-auto">
                <div className="schedulePlayList__item__threeDot">
                  <MoreHorizRoundedIcon fontSize="small" />
                </div>
              </div>
            </div>
            <Box sx={{ flexGrow: 1 }}>
              <AudienceChart />
            </Box>
          </Paper>
          {/* <Grid item xs={12} mt={4}>
                        <div className='formBoxTitle'>Related Playlists</div>
                    </Grid>
                    <Grid item xs={12} >
                        <div className='d-flex mt-3'>
                            <PlaylistItem isSelected={true} id={1} playlistItemClick={playlistItemClick} />
                            <PlaylistItem isSelected={false} id={2} playlistItemClick={playlistItemClick} />
                        </div>

                    </Grid> */}
        </Grid>
      </Grid>
      <AppConfirmation
        open={confirmation.open}
        onClose={handleConfirmationClose}
        onSubmit={confirmation.onSubmit}
        title={confirmation.title}
        message={confirmation.message}
      />
    </>
  );
};

export default ContentDetailsScreen;
