import React, { useRef, useState } from 'react'
import AddPhotoAlternateRoundedIcon from '@mui/icons-material/AddPhotoAlternateRounded';
import DeleteOutlineRoundedIcon from '@mui/icons-material/DeleteOutlineRounded';
import { checkValueLength } from '../../util/StringUtils';
import VideoCameraBackRoundedIcon from '@mui/icons-material/VideoCameraBackRounded';
import RemoveRedEyeRoundedIcon from '@mui/icons-material/RemoveRedEyeRounded';
import VideoPopup from './VideoPopup';
import UPLOAD_VIDEO_ICON from '../../assets/images/svgs/Upload_a_video.svg'

const AppVideoMediaSelection = ({
    headings = "",
    secondHeading = "",
    selectedMedia = null,
    onMediaChange = null,
    displayMediaFile = null,
    mediaErr = "",
    mediaFileName = "",
    noDeleteBtn = false
}) => {
    const mediaRefPress = useRef(null);
    const onInputChange = (e) => {
        if (onMediaChange) {
            onMediaChange("SELECT_MEDIA", { mediaData: e.target.files });
        }
    }
    const mediaBtnPress = () => {
        mediaRefPress.current.click();
    }
    const deleteMediaBtnPress = () => {
        if (onMediaChange) {
            onMediaChange("DELETE_MEDIA", null)
        }
    }
    const [showVideoPopupData, setShowVideoPopupData] = useState({
        refreshKey: 1,
        displayPopup: false,
        videoUrl: null,
        videoPoster: null,
        videoPoster: null,
        autoPlay: true,
    });
    const viewMediaBtnPress = () => {
        setShowVideoPopupData(prevState => ({
            ...prevState,
            refreshKey: Math.random(),
            displayPopup: true,
            videoUrl: displayMediaFile,
            videoPoster: displayMediaFile,
            autoPlay: true,
        }));
    }
    return (
        <div className="media-selection-container">
            <div className='d-flex align-items-center inputMediaBox'>
                <input
                    ref={mediaRefPress}
                    className="d-none"
                    onClick={(event) => {
                        event.target.value = null
                    }}
                    type="file"
                    onChange={(e) => onInputChange(e)} />

                <div className='d-flex align-items-center justify-content-center inputMediaBox__iconBox'
                    onClick={() => mediaBtnPress()}>
                    {
                        displayMediaFile != null ?
                            <>
                                <VideoCameraBackRoundedIcon />
                                {/* <video src={displayMediaFile} alt="media" className='inputMediaBox__media' /> */}
                            </>

                            :
                            <img src={UPLOAD_VIDEO_ICON} alt="media" className='inputMediaBox__add_media' />
                    }

                </div>
                <div className='d-flex flex-column ms-3'>
                    {

                        checkValueLength(mediaFileName) ?
                            <div className='inputMediaBox__headings'>{mediaFileName}</div>
                            :
                            <>
                                <div className='inputMediaBox__headings'>{headings}</div>
                                <div className='inputMediaBox__secondHeading'>{secondHeading}</div>
                            </>

                    }

                </div>

                <div className='d-flex ps-3 inputMediaBox__deleteIcon'>
                    {
                        displayMediaFile != null ?
                            <div className='me-2'>
                                <RemoveRedEyeRoundedIcon fontSize='medium' onClick={() => viewMediaBtnPress()} />
                            </div>
                            : null
                    }
                    {
                        selectedMedia != null && !noDeleteBtn ?
                            <DeleteOutlineRoundedIcon fontSize='medium' onClick={() => deleteMediaBtnPress()} />
                            :
                            null
                    }
                </div>

            </div>
            {
                mediaErr.length != 0 ?
                    <div className='text-danger mt-2'>{mediaErr}</div>
                    :
                    null
            }
            <VideoPopup
                key={showVideoPopupData.refreshKey}
                displayPopup={showVideoPopupData.displayPopup}
                videoUrl={showVideoPopupData.videoUrl}
                videoPoster={showVideoPopupData.videoPoster}
                autoPlay={showVideoPopupData.autoPlay}
            />
        </div>
    )
}

export default AppVideoMediaSelection