import React from 'react'
import { useTheme } from '@mui/material/styles';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { AppBar, Box, useMediaQuery } from '@mui/material';
import AppConstants from '../data/AppConstants';
import UserPlaceholder from "../assets/images/user_image_place_holder.png";
import NotificationsIcon from '@mui/icons-material/Notifications';
import { AppButton } from './ui_component/AppButton';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import AppRoutes from '../data/AppRoutes';

const AppHeader = () => {
    const theme = useTheme();
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { pathname } = useLocation();
    const open = useSelector((state) => state.drawer.value.open);
    const matchUpMd = useMediaQuery(theme.breakpoints.up('md'));
    const addEpisodeBtn = () => {
        navigate(AppRoutes.ADD_EPISODE_PATH, { state: { episodeSeq: -1 } });
    }
    const addShowBtn = () => {
        navigate(AppRoutes.ADD_SHOW_PATH, { replace: true });
    }
    return (
        <AppBar position="fixed" open={open}
            sx={{
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.leavingScreen,
                width: { sm: (open && matchUpMd) ? `calc(100% - ${AppConstants.DRAWER_WIDTH}px)` : '100%' },
                ml: { sm: (open && matchUpMd) ? `${AppConstants.DRAWER_WIDTH}px` : 0 },
                transition: theme.transitions.create(['width', 'margin'], {
                    easing: theme.transitions.easing.sharp,
                    duration: theme.transitions.duration.enteringScreen,
                }),
                transition: theme.transitions.create(['width', 'margin'], {
                    easing: theme.transitions.easing.sharp,
                    duration: theme.transitions.duration.leavingScreen,
                }),
                background: '#111111',
                boxShadow: 'none'

            }}>
            <Box sx={{ display: 'flex', flexDirection: 'row', padding: "12px 20px", width: '100%' }}>
                <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                    {
                        ![AppRoutes.ADD_EPISODE_PATH, AppRoutes.ADD_SHOW_PATH, AppRoutes.ADD_SEASON_PATH].includes(pathname) ?
                            <>
                                <AppButton className="me-2" label='Episode' startIcon={<AddRoundedIcon />}
                                    onClick={() => addEpisodeBtn()} />
                                {/* <AppButton label='show' startIcon={<AddRoundedIcon />}
                                    onClick={() => addShowBtn()} /> */}
                            </>
                            : null
                    }

                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'row', marginLeft: 'auto', alignItems: 'center' }}>
                    {/* <Box sx={{ marginRight: "12px" }}>
                        <NotificationsIcon />
                    </Box> */}
                    <Box>
                        <img src={UserPlaceholder} alt="user" style={{
                            width: "32px",
                            height: "32px",
                            borderRadius: "50%"
                        }} />

                    </Box>
                </Box>


            </Box>

        </AppBar>
    )
}

export default AppHeader