import React from 'react'
import ImagePlaceholder from '../../assets/images/default_image.jpg'

const PlaylistItem = ({
    id,
    playlistItemClick,
    data = {}
}) => {
    return (
        <div className={`parentPlaylistItem d-flex flex-column align-items-center justify-content-center mb-3`}
            onClick={() => playlistItemClick(id)}>

            <img src={data.thumb_file} alt='item' className='parentPlaylistItem__image' />
            <div className='parentPlaylistItem__text'>
                {data.title}
            </div>

        </div>
    )
}

export default PlaylistItem