@import url('https://fonts.googleapis.com/css2?family=Roboto&display=swap');

/* @font-face {
    font-family: 'PlusJakartaSans';
    src: local('PlusJakartaSans'), url(../fonts/PlusJakartaSans-Regular.ttf) format('woff');
} */

:root {
    --background: #111111;
    --primary: #FC6767;
    --textOnPrimary: #FFFFFF;
    --inputLabelColor: "#FFFFFF";
    --inputBackgroundColor: "#F6F6F6";

    --input-font-size: 0.75rem;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

input {
    font-family: 'Roboto', sans-serif;
}

html {
    font-size: 100%;
}

body {
    font-family: 'Roboto', sans-serif;
    background: var(--background);
    color: var(--textOnPrimary);
}

.loginLogo {
    height: 85px;
    object-fit: contain;
    margin-bottom: 1.5rem;
}

.promptText {
    margin-bottom: 2rem;
}

.drawerLogo {
    height: 41px;
    object-fit: contain;
    margin-bottom: 1.5rem;
}

.drawerMenuItem {
    padding: 8px 8px;
    margin-bottom: 2px;
    cursor: pointer;
}

.drawerMenuItemIcon {
    margin-right: 12px;
}

.drawerMenuItemLabel {
    font-size: 0.875rem;
    font-weight: 100;
    color: #FFFFFF;
    opacity: 0.4;
}

.drawerMenuItem.active,
.drawerMenuItem:hover {
    background-color: #FFFFFF;
    border-radius: 8px;

}

.drawerMenuItem.active .drawerMenuItemLabel,
.drawerMenuItem:hover .drawerMenuItemLabel {
    color: #0A0A0C;
    font-weight: bold;
    opacity: 1;
}

.drawerMenuItem.active .drawerMenuItemIcon,
.drawerMenuItem:hover .drawerMenuItemIcon {
    color: var(--primary);
    font-weight: bold;
}

.overviewCard {
    border-radius: 8px !important;
    background-color: #FFFFFF;
    min-height: 80px;
    padding: 15px !important;
}

.countCard {
    min-width: 120px;
    min-height: 90px;
}

.cardGapLeft {
    margin-left: 1rem;
}

.cardGapRight {
    margin-right: 1rem;
}

.cardGapBottom {
    margin-bottom: 1rem;
}

.cardGapTop {
    margin-top: 1rem;
}

/*------------------------------- Overview Section -----------------*/
.overViewCardImage {
    height: 60px;
    width: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.overviewCard__name {
    font-size: 1rem;
    font-weight: bold;
    color: #0A0A0C;
    opacity: 1;
}

.overviewCard__handle {
    font-size: 0.75rem;
    font-weight: 500;
    color: #0A0A0C;
    opacity: 0.4;
}

.overviewCard__viewPlaylist__text {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--primary);
}

.overviewCard__viewPlaylist__icon {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--primary);
}

.countBox__icon {
    height: 16px;
    width: 16px;
    object-fit: contain;
}

.countBox__text {
    color: #0A0A0C;
}

.countBox__number {
    margin-top: 8px;
    font-size: 1.5rem;
    color: #0A0A0C;
    font-weight: bold;
}

.countPercentage__text {
    color: #88C591;
    font-size: 0.65rem;
}

.countPercentage__time {
    color: #0A0A0C;
    font-size: 0.65rem;
    opacity: 0.3;
    margin-left: 8px;
}

.CardHeader__title {
    color: #0A0A0C;
    font-size: 1rem;
    font-weight: bold;
}

.schedulePlayList__item__image {
    height: 48px;
    width: 48px;
    background-color: #D9D9D9;
    border-radius: 8px;
    object-fit: contain;
}

.schedulePlayList__item {
    margin-bottom: 0.65rem;
}

.schedulePlayList__item__title {
    color: #222222;
    font-size: 0.75rem;
    font-weight: 600;

}

.schedulePlayList__item__duration {
    color: #A4A4A4;
    font-size: 0.65rem;
    margin-left: 6px;
}

.schedulePlayList__item__icon {
    color: #A4A4A4;
    font-size: 0.5rem;
}

.schedulePlayList__item__threeDot {
    color: #000000;
}

.CardHeader {
    margin-bottom: 0.5rem;
}

.lineChartTooltip__text {
    color: #FFFFFF;

    font-size: 0.5rem;
}

.lineChart__dot {
    background-color: #FFFFFF;
    height: 8px;
    width: 8px;
    border-radius: 50%;
    margin-right: 8px;

}

.lineChart__title {
    color: #222222;
    font-size: 0.75rem;
}

.pageTitle {
    color: #FFFFFF;
    font-size: 1.5rem;
    font-weight: bold;
    letter-spacing: 0px;
}

.pageTitleGap {
    margin-top: 0.5rem;
}

.formBoxTitle {
    color: #FFFFFF;
    font-size: 1rem;
    font-weight: 500;
    letter-spacing: 0px;
    margin-top: 1rem;
}

/* ------------------ Media Box ---------------------- */
.inputMediaBox {
    background-color: transparent;
    border-radius: 8px;
    width: 100%;
    min-height: 40px;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='5' ry='5' stroke='%23FC6767FF' stroke-width='2' stroke-dasharray='6%2c 14' stroke-dashoffset='13' stroke-linecap='square'/%3e%3c/svg%3e");
    padding: 16px 15px;
    background-color: #000000;
}

.inputMediaBox__iconBox {
    width: 48px;
    height: 48px;
    background-color: #FFFFFF;
    border-radius: 50%;
    color: var(--primary);
    font-size: 22px;
    cursor: pointer;
}

.inputMediaBox__headings {
    color: var(--primary);
    font-size: 0.75rem;
}

.inputMediaBox__headings--filename {
    color: var(--primary);
    font-size: 0.75rem;
    width: 150px;
    word-wrap: break-word;
}

.inputMediaBox__secondHeading {
    color: var(--primary);
    font-size: 0.65rem;
    opacity: 0.7;
    margin-top: 0.3rem;
}

.inputMediaBox__deleteIcon {
    color: var(--primary);
    margin-left: auto;
    cursor: pointer;
}

.inputMediaBox__media {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: contain;
}

.inputMediaBox__add_media {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

/* ------------------ Account Box ---------------------- */
.userImageBox {
    height: 400px;
    position: relative;
    border-radius: 2px;

}

.userGradient {
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    position: absolute;
    background: transparent linear-gradient(180deg, #******** 0%, #111111 100%) 0% 0% no-repeat padding-box;
    opacity: 1;
    border-radius: 2px;
}

.userImage {
    height: 400px;
    width: 100%;
    object-fit: cover;
    border-radius: 2px;

}

.userCountBox {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0.5rem;
}

.userCountBoxInner {
    margin-bottom: 10px;
}

.userCountBoxInnerItem__value {
    font-size: 1rem;
}

.userCountBoxInnerItem__label {
    font-size: 0.6rem;
    color: #C7C7C7;
}

.formTabBox {
    margin-top: 1rem;
    border: 1px solid #707070;
    border-radius: 14px;
    opacity: 1;
    padding: 2px 2px;
    transition: all 1s linear;
}

.formTabBox__item {
    min-width: 85px;
    text-align: center;
    font-size: 0.65rem;
    border-radius: 14px;
    padding: 4px 2px;
    cursor: pointer;
}

.formTabBox__item.active {
    background-color: var(--primary);
}

.sideFormButtonGap {
    margin-top: 25px;
}

/* --------------------- Content ------------------------------ */
.parentPlaylistItem {
    border-radius: 10px;
    margin-right: 20px;
    cursor: pointer;
    position: relative;

}

.parentPlaylistItem.selected {
    border: 2px solid var(--primary);
}

.playlistOverlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent linear-gradient(180deg, #******** 0%, #111111 100%) 0% 0% no-repeat padding-box;
    z-index: 1;
    border-radius: 10px;
}

.parentPlaylistItem__image {
    width: 120px;
    height: 160px;
    object-fit: cover;
    border-radius: 10px;
    background: transparent linear-gradient(180deg, #******** 3%, #111111 100%) 0% 0% no-repeat padding-box;
}

.parentPlaylistItem__text {
    letter-spacing: 0px;
    color: #A1A1A1;
    text-transform: uppercase;
    opacity: 1;
    font-size: 0.75rem;
    margin-top: 0.5rem;
    text-align: center;
    width: 80px;
    overflow: hidden;
}

.parentPlaylistItem .actionBox {
    position: absolute;
    top: 1px;
    right: 1px;
    z-index: 3;
}

.parentPlaylistItem .actionBox__iconBox {
    color: #FFFFFF;
}

.showClipItem__moreIcon {
    color: var(--primary);
    cursor: pointer;
}

/* --------------------- Content Details ------------------------------ */
.showBox__image {
    width: 120px;
    height: 200px;
    box-shadow: 0px 3px 6px #0000004F;
    border: 3px solid #FFFFFF;
    border-radius: 14px;
    opacity: 1;
    object-fit: cover;
}

.showBox__content {
    margin-top: 10px;
    margin-left: 24px;

}

.showBox__content--title {
    font-size: 1.5rem;
    font-weight: 600;
    letter-spacing: 0px;
    color: #FFFFFF;
    opacity: 1;
    margin-top: 24px;
}

.showBox__content--desc {
    text-align: left;
    font-size: 0.875rem;
    letter-spacing: 0px;
    color: #707070;
    opacity: 1;
    margin-top: 12px;
}

.showBox__content--actionbox__button {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border-radius: 8px;
    opacity: 1;
    padding: 8px 10px;
    cursor: pointer;
}

.showBox__content--actionbox__button.normalBtn {
    color: #000000;
    font-size: 0.75rem;
    font-weight: bold;
}

.showBox__content--actionbox__button.errorBtn {
    background-color: #FC67672E;
    color: var(--primary);
    opacity: 1;
    font-size: 0.75rem;
    font-weight: bold;
}

.showClipItem__image {
    width: 92px;
    height: 163px;
    object-fit: cover;
    box-shadow: 0px 3px 6px #0000004F;
    border: 3px solid #FFFFFF;
    border-radius: 14px;
    opacity: 1;
}

.showClipItem__content {
    margin-top: 2px;
    margin-left: 24px;

}

.showClipItem__content--title {
    font-size: 1.25rem;
    font-weight: 600;
    letter-spacing: 0px;
    color: #FFFFFF;
    opacity: 1;
    margin-top: 24px;
}

.showClipItem__content--text {
    text-align: left;
    font-size: 0.75rem;
    letter-spacing: 0px;
    color: #707070;
    opacity: 1;

}

.showClipItem__content--text.value {
    margin-right: 0.2rem;
}

.paperHeader__title {
    color: #3F3F44;
    font-size: 0.875rem;
    font-weight: bold;
}

/* -------------- Guide Media Box --------------------- */
.guideMediaBox {
    width: 80px;
    height: 62px;
    position: relative;
    border-radius: 14px;
}

.playBox {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.5rem;
    background-color: #3F3F44;
    padding: 0.1rem;
    border-radius: 50%;
}

.playBoxBackdrop {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-radius: 14px;
    background-color: rgba(0, 0, 0, 0.4);
}

.guideMediaBox__image {
    width: 80px;
    height: 62px;
    border-radius: 14px;
    object-fit: cover;
}

.guideTextBox {
    margin-left: 10px;
}

.guideTextBox__title {
    color: #3F3F44;
    font-size: 0.75rem;
}

.submittedTitle {
    font-size: 0.875rem;
    color: #FFFFFF80;
    opacity: 1;
    font-weight: bold;
    margin-bottom: 0.2rem;
}

.submittedDate {
    font-size: 0.75rem;
    letter-spacing: 0px;
    color: #FFFFFF80;
    opacity: 1;

}

.submittedStatus {
    background: #CCEABB40 0% 0% no-repeat padding-box;
    border-radius: 8px;
    opacity: 1;
    letter-spacing: 0px;
    color: #99C183;
    font-size: 0.65rem;
    padding: 4px 12px;
}

/* ------------------ Heat Map ---------------------- */
.heatmap-container {
    flex-wrap: wrap;
    /* border: 1px solid #000; */
}

.heatmap-row {
    margin-bottom: 8px;
}

.heatmap-yLabels {
    min-width: 32px;
    text-align: end;
    color: #222222;
    opacity: 0.5;
    font-size: 0.65rem;
    padding-left: 5px;
    padding-right: 5px;
}

.heatmap-cell {
    width: 60px;
    height: 24px;
    margin-right: 8px;
    /* border: 1px solid #000; */
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    cursor: pointer;
}

.heatmap-xLabels {
    position: absolute;
    left: 0;
    right: 0;
    top: 24px;
    text-align: center;
    color: #222222;
    opacity: 0.5;
    font-size: 0.65rem;
    margin-right: 8px;
    padding-left: 5px;
    padding-right: 5px;
    padding-top: 8px;
    z-index: 1;
}

.heatmap-cell-value {
    font-size: 12px;
    color: #fff;
}

.heatmap-cell:hover {
    cursor: pointer;
}

.heatmap-cell:hover::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #fff;
}

/* ------------------ Heat Map End ---------------------- */
/* ------------------ dropdownButton ---------------------- */
.dropdownButton {
    margin-top: 10px !important;
    position: relative;
    padding-left: 16px;
    padding-right: 16px;
    cursor: pointer;
    padding-top: 8px;
    padding-bottom: 8px;
}

.dropdownButton__text {
    font-size: 12px;
    font-weight: bold;
    color: var(--primary);
}

/* ------------------ Clip Box ---------------------- */
.clipItemBox {
    position: relative;
    height: 54px;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border-radius: 8px;
    opacity: 1;
    padding: 16px;
}

.clipItemBox__itemTitle {
    text-align: left;
    font-size: 14px;
    letter-spacing: 0px;
    color: #3F3F44B3;
    opacity: 1;
}

.clipItemBox__inputBox {
    width: 100%;
    margin-left: 10px;
}

.clipItemBox__input {
    width: 100%;
    height: 32px;
    background: #F6F6F6 0% 0% no-repeat padding-box;
    border-radius: 8px;
    opacity: 1;
    padding-left: 10px;
    border: 1px;
    font-size: 14px;
}

.clipItemBox__input:focus,
.clipItemBox__input:active,
.clipItemBox__input:hover {
    outline: none;

}

.clipItemBox__input::placeholder {
    color: #3F3F444D;
}

.uploadClipBtn {
    height: 32px;
    background: #FC67672E 0% 0% no-repeat padding-box;
    border-radius: 8px;
    opacity: 1;
    padding: 10px 20px;
    cursor: pointer;
}

.clipMediaBox {
    height: 40px;
    width: 40px;
    border: 1px solid #111111;
    border-radius: 4px;
}

.clipMediaBox video {
    object-fit: contain;
    height: 40px;
    width: 40px;
}

.uploadClipBtn__text {
    text-align: center;
    font-size: 12px;
    letter-spacing: 0px;
    color: var(--primary);
    opacity: 1;
    font-weight: bold;
}

.editBtn {
    background: #7070702E 0% 0% no-repeat padding-box;
    color: #000000;
}

.editBtn .uploadClipBtn__text {
    color: #000000;
}

.clipItemBox__crossBox {
    position: absolute;
    right: 4px;
    top: 4px;
}

.clipItemBox__crossBox__cross {
    color: red;
    font-size: 12px;
    cursor: pointer;
}

.inputExtraText {
    text-align: left;
    font-size: 10px;
    letter-spacing: 0px;
    color: #FFFFFF;
    opacity: 0.64;
}

.MuiDayCalendar-weekDayLabel {
    color: #FFFFFF !important;
}

.multiSelectSearchContainer {

    height: 28px;
    margin: 10px 20px;
    border-radius: 8px;
    border: 1px solid #000;
    position: sticky;
    top: 0;
    color: #000;
    background-color: #FFFFFF;
}

.multiSelectSearchContainer input {
    width: 100%;
    color: #000;

}

.MuiAutocomplete-tag.MuiAutocomplete-tagSizeMedium {
    font-size: 12px;
    color: #111111;
    cursor: pointer;
}

.MuiChip-deleteIcon {
    font-size: 12px;
    color: var(--primary) !important;
    cursor: pointer;
}

.tagPeopleBox {
    min-height: 200px;
    max-height: 200px;
    overflow-y: auto;
}

.tagPeopleItem {
    min-height: 32px;
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 8px 10px;
    margin-bottom: 10px;
    cursor: pointer;
}

.tagPeopleItem__profileImage {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
    object-fit: cover;
}

.tagPeopleItem__name {
    font-size: 12px;
    color: #111111;

}

.visibleItemErrorMessage {
    color: #FFFFFF;
    font-weight: bold;
    font-size: 12px;

}

.MuiMenu-paper.MuiPopover-paper {
    max-height: 300px;
}

/* -------------- Media ------------------------*/
.popupVideoBox .videoBox {
    max-height: 70vh;
    text-align: center;
}

.popupVideoBox .videoBox video {
    height: 100%;
    width: 100%;
    max-height: 70vh;
}

.videoBox {
    max-height: 70vh;
    text-align: center;
}

.videoBox video {
    height: 100%;
    width: 100%;
    max-height: 70vh;
}

.viewPopupImage {
    height: 70vh;
    margin: auto;
    object-fit: scale-down;
}

.editIcon {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.dropdownIcon {
    width: 20px;
    height: 20px;
    object-fit: contain;
    margin-right: 8px;
}

.dateIcon {
    width: 20px;
    height: 20px;
    object-fit: contain;
    cursor: pointer;
}

.buttonIcon {
    width: 20px;
    height: 20px;
    object-fit: contain;
    cursor: pointer;
}

.buttonSmallIcon {
    width: 20px;
    height: 20px;
    object-fit: contain;
    cursor: pointer;
}

.buttonDeleteIcon {
    width: 20px;
    height: 20px;
    object-fit: contain;
    cursor: pointer;
}

.listRowIcon {
    width: 24px;
    height: 24px;
    object-fit: contain;
    cursor: pointer;
}

.resendOtpText {
    font-size: 12px;
}

.resendOtpBtnBox {
    margin-left: 8px;
}

.resendOtpBtnBox__timer {
    font-size: 12px;
    color: var(--primary);
    font-weight: bold;

}

.resendOtpBtnBox__btn {
    margin-left: 14px;
    font-size: 12px;
    color: var(--primary);
    font-weight: bold;
    cursor: pointer;
}

.resendOtpBtnBox__btn.disabled {
    opacity: 0.5;
    cursor: auto;
}