import { Backdrop, CircularProgress } from '@mui/material'
import React from 'react'

const AppLoader = ({ isLoading = false }) => {
    return (
        <>
            {
                isLoading ?
                    <Backdrop open={isLoading} sx={{ color: (theme) => theme.palette.primary.dark, zIndex: (theme) => theme.zIndex.drawer + 1 }}>
                        <CircularProgress color="inherit" />
                    </Backdrop>
                    : null
            }

        </>
    )
}

export default AppLoader