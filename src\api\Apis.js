import { checkValueLength } from "../util/StringUtils";
import ServerConnector from "./ServerConnector";


async function makeApiCall(dataMap) {
    const connector = new ServerConnector();
    const result = await connector.postData(dataMap);
    return result;
}
async function makeMultipartApiCall(dataMap, imageHasMap) {
    const connector = new ServerConnector();
    const result = await connector.postDataMultiPart(dataMap, imageHasMap);
    return result;
}
export async function generateOtpApi(userId) {
    const dataMap = {
        _action_code: "GENERATE_LOGIN_OTP",
        user_id: userId,
    };
    return await makeApiCall(dataMap);
}
export async function loginApi(userId, password) {
    const dataMap = {
        _action_code: "USER_LOGIN",
        user_id: userId,
        password: password,
    };
    return await makeApiCall(dataMap);
}
export async function getAllShowApi() {
    const dataMap = {
        _action_code: "GET_ALL_SHOWS",
    };
    return await makeApiCall(dataMap);
}

export async function AddUpdateShowApi(showSeq, title, gridTitle, description, is_paid, priceValue, expiryDate, scheduleOn, locationValue, topics, yearOfRelease, imageHasMap) {
    var dataMap = {
        _action_code: "ADD_SHOW",
        title: title,
        description: description,
        is_paid: is_paid,
    };
    if (checkValueLength(priceValue)) {
        dataMap.price = priceValue;
    }
    if (checkValueLength(expiryDate)) {
        dataMap.expiry_date = expiryDate;
    }
    if (checkValueLength(scheduleOn)) {
        dataMap.schedule_on = scheduleOn;
    }
    if (checkValueLength(locationValue)) {
        dataMap.location = locationValue;
    }
    if (checkValueLength(yearOfRelease)) {
        dataMap.release_year = yearOfRelease;
    }
    if (checkValueLength(gridTitle)) {
        dataMap.grid_title = gridTitle;
    }
    if (topics.length > 0) {
        dataMap.topics = topics;
    }
    if (showSeq != -1) {
        dataMap.show_seq = showSeq;
        dataMap._action_code = "UPDATE_SHOW";
    }
    return await makeMultipartApiCall(dataMap, imageHasMap);
}
export async function getShowDetailsApi(showSeq) {
    const dataMap = {
        _action_code: "GET_SHOW",
        show_seq: showSeq
    };
    return await makeApiCall(dataMap);
}
export async function setShowStatusApi(showSeq, status) {
    const dataMap = {
        _action_code: "SET_SHOW_STATUS",
        show_seq: showSeq,
        status: status,
    };
    return await makeApiCall(dataMap);
}
export async function getLocationListApi() {
    const dataMap = {
        _action_code: "GET_LOCATION_LIST",
    };
    return await makeApiCall(dataMap);
}
export async function getInterestListApi() {
    const dataMap = {
        _action_code: "GET_INTEREST_LIST",
    };
    return await makeApiCall(dataMap);
}
//Season
export async function getShowListApi() {
    const dataMap = {
        _action_code: "GET_SHOWS_LIST",
    };
    return await makeApiCall(dataMap);
}
export async function getAllSeasonsApi(showSeq) {
    const dataMap = {
        _action_code: "GET_ALL_SEASONS",
        show_seq: showSeq,
    };
    return await makeApiCall(dataMap);
}

export async function addUpdateSeasonsApi(seasonSeq, showSeq, title, gridTitle, description, yearOfRelease, imageHasMap) {
    const dataMap = {
        _action_code: "ADD_SEASON",
        title: title,
        description: description,
    };
    if (checkValueLength(showSeq) && showSeq != -1) {
        dataMap.show_seq = showSeq;
    }
    if (checkValueLength(yearOfRelease)) {
        dataMap.release_year = yearOfRelease;
    }
    if (checkValueLength(gridTitle)) {
        dataMap.grid_title = gridTitle;
    }
    if (seasonSeq != -1) {
        dataMap.season_seq = seasonSeq;
        dataMap._action_code = "UPDATE_SEASON";
    }
    return await makeMultipartApiCall(dataMap, imageHasMap);
}
export async function getSeasonDetailsApi(seasonSeq) {
    const dataMap = {
        _action_code: "GET_SEASON",
        season_seq: seasonSeq
    };
    return await makeApiCall(dataMap);
}
export async function setSeasonStatusApi(seasonSeq, status) {
    const dataMap = {
        _action_code: "SET_SEASON_STATUS",
        season_seq: seasonSeq,
        status: status,
    };
    return await makeApiCall(dataMap);
}
//Episode
export async function getSeasonListApi(showSeq) {
    const dataMap = {
        _action_code: "GET_SEASONS_LIST",
        show_seq: showSeq,
    };
    return await makeApiCall(dataMap);
}
export async function getAllEpisodeApi(showSeq, seasonSeq) {
    const dataMap = {
        _action_code: "GET_ALL_EPISODES",
        show_seq: showSeq,
    };
    if (checkValueLength(seasonSeq) && seasonSeq != -1) {
        dataMap.season_seq = seasonSeq;
    }
    return await makeApiCall(dataMap);
}
export async function AddUpdateEpisodeApi(episodeSeq, showSeq, seasonSeq, title, gridTitle, description, is_paid, priceValue, expiryDate, scheduleOn, locationValue, reactions, people_tags, yearOfRelease, imageHasMap) {
    var dataMap = {
        _action_code: "ADD_EPISODE",
        title: title,
        description: description,
        is_paid: is_paid,
    };
    if (checkValueLength(showSeq) && showSeq != -1) {
        dataMap.show_seq = showSeq;
    }
    if (checkValueLength(seasonSeq) && seasonSeq != -1) {
        dataMap.season_seq = seasonSeq;
    }
    if (checkValueLength(priceValue)) {
        dataMap.price = priceValue;
    }
    if (checkValueLength(locationValue)) {
        dataMap.location = locationValue;
    }
    if (checkValueLength(expiryDate)) {
        dataMap.expiry_date = expiryDate;
    }
    if (checkValueLength(scheduleOn)) {
        dataMap.schedule_on = scheduleOn;
    }
    if (reactions.length > 0) {
        dataMap.reactions = reactions;
    }
    if (people_tags.length > 0) {
        dataMap.people_tags = people_tags;
    }
    if (checkValueLength(yearOfRelease)) {
        dataMap.release_year = yearOfRelease;
    }
    if (checkValueLength(gridTitle)) {
        dataMap.grid_title = gridTitle;
    }
    if (episodeSeq != -1) {
        dataMap.episode_seq = episodeSeq;
        dataMap._action_code = "UPDATE_EPISODE";
    }
    return await makeMultipartApiCall(dataMap, imageHasMap);
}
export async function getEpisodeDetailsApi(episodeSeq) {
    const dataMap = {
        _action_code: "GET_EPISODE",
        episode_seq: episodeSeq
    };
    return await makeApiCall(dataMap);
}
export async function setEpisodeStatusApi(episodeSeq, status) {
    const dataMap = {
        _action_code: "SET_EPISODE_STATUS",
        episode_seq: episodeSeq,
        status: status,
    };
    return await makeApiCall(dataMap);
}
export async function getUserTagListApi(profileSeq) {
    const dataMap = {
        _action_code: "GET_USER_TO_TAG",
        profile_seq: profileSeq,
    };
    return await makeApiCall(dataMap);
}
export async function AddEpisodeClipApi(episodeSeq, profile_seq, title, file_format, post_type, imageHasMap) {
    var dataMap = {
        _action_code: "SUBMIT_CLIP",
        episode_seq: episodeSeq,
        profile_seq: profile_seq,
        comments: title,
        file_format: file_format,
        post_type: post_type,
        media_type: "VIDEO",
        is_playlist: "YES",
    };
    return await makeMultipartApiCall(dataMap, imageHasMap);
}
export async function getEpisodeClipApi(episodeSeq) {
    const dataMap = {
        _action_code: "GET_CLIPS",
        episode_seq: episodeSeq
    };
    return await makeApiCall(dataMap);
}
export async function updateEpisodeClipApi(postSeq, title) {
    const dataMap = {
        _action_code: "UPDATE_CLIP",
        post_seq: postSeq,
        comments: title,
    };
    return await makeApiCall(dataMap);
}
export async function setEpisodeClipStatusApi(postSeq, status) {
    const dataMap = {
        _action_code: "SET_CLIP_STATUS",
        post_seq: postSeq,
        status: status,
    };
    return await makeApiCall(dataMap);
}
export async function getMasterBannerApi() {
    const dataMap = {
        _action_code: "GET_MASTER_BANNER",
    };
    return await makeApiCall(dataMap);
}
export async function getYearOfReleaseListApi() {
    const dataMap = {
        _action_code: "GET_RELEASE_YEARS",
    };
    return await makeApiCall(dataMap);
}