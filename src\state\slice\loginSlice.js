import { createSlice } from '@reduxjs/toolkit'

export const loginSlice = createSlice({
    name: 'loginSlice',
    initialState: {
        value: {
            isLoggedIn: false,
        },
    },
    reducers: {
        login: (state) => {
            state.value.isLoggedIn = true;
        },
        logout: (state) => {
            state.value.isLoggedIn = false;
        },
    },
});

// Action creators are generated for each case reducer function
export const { login, logout } = loginSlice.actions

export default loginSlice.reducer