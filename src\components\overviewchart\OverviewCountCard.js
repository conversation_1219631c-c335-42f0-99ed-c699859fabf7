import React from 'react'

const OverviewCountCard = ({
    heading = "",
    count = "",
    icon = null,
    style = {},
    className = "",
    percentageType = "UP",
    percentage = "",
    percentageTime = "",
    ...props


}) => {
    return (
        <div className={`overviewCard d-flex countCard ${className}`} style={style}>
            <div className='countBox'>
                <div className='d-flex'>
                    <img src={icon} className='img-fluid countBox__icon me-2' alt="icon" />
                    <div className='countBox__text'>{heading}</div>
                </div>
                <div className='countBox__number'>{count}</div>
                <div className='countPercentage d-flex'>
                    <div className='countPercentage__text' style={{ color: percentageType == "DOWN" ? "#D38383" : '#88C591' }}>{percentage}</div>
                    <div className='countPercentage__time' >{percentageTime}</div>
                </div>
            </div>

        </div>
    )
}

export default OverviewCountCard