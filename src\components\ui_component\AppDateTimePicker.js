import React, { useRef } from 'react'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { IconButton, InputAdornment, TextField } from '@mui/material';
import { MobileDateTimePicker } from '@mui/x-date-pickers/MobileDateTimePicker';
import CalendarMonthRoundedIcon from '@mui/icons-material/CalendarMonthRounded';
import AppConstants from '../../data/AppConstants';
import useAppTheme from '../../ui_theme/useAppTheme';
import { AppInput } from './AppInput';
import { DisplayDateTimeFormat } from '../../util/DateUtils';
import dayjs from 'dayjs';
import DATE_ICON from '../../assets/images/svgs/Calendar.svg'


const AppDateTimePicker = ({
    label = "",
    placeholder = "",
    value = null,
    style = null,
    onDateChange = null,
    errorMessage = "",
    disabled = false,
    disableColor = null,
    ...props
}) => {
    const datePickerRef = useRef(null);
    const appTheme = useAppTheme();
    const onDatePickerChange = (selectedDate) => {
        if (onDateChange) {
            onDateChange(selectedDate);
        }
    }
    const openDatePicker = () => {
        datePickerRef.current.click();
    }
    const DateIconComponent = () => {
        return (
            <IconButton
                aria-label="date Field"
                edge="end">
                <CalendarMonthRoundedIcon />
            </IconButton>
        )
    }
    const DateIcon = () => {
        return (
            <img src={DATE_ICON} className='dateIcon' alt="date Field" />
        )
    }
    return (
        <>
            <AppInput
                label={label}
                placeholder={placeholder}
                value={value ? DisplayDateTimeFormat(value) : ""}
                readOnly={true}
                onClick={(e) => openDatePicker()}
                marginBottom='22px'
                endComponent={<DateIcon />} />
            <LocalizationProvider dateAdapter={AdapterDayjs}>
                <MobileDateTimePicker
                    className='d-none'
                    ref={datePickerRef}
                    inputFormat={AppConstants.DATE_FORMAT_TYPE}
                    disableMaskedInput
                    label={""}
                    value={dayjs(value)}
                    disabled={disabled}
                    onAccept={onDatePickerChange}
                    showToolbar={false}
                    inputProps={{
                        style: {
                            color: appTheme.colors.dateTextColor,
                        }
                    }}
                    slotProps={{
                        layout: {
                            sx: {
                                color: appTheme.colors.dateTextColor,
                                borderRadius: '14px',
                                border: '1px solid #111111',
                                backgroundColor: '#111111',
                            }
                        },
                        mobilePaper: {
                            sx: {
                                color: appTheme.colors.dateTextColor,
                                borderRadius: '14px',
                                backgroundColor: '#111111',
                            },
                        },
                        switchViewIcon: {
                            sx: {
                                color: appTheme.colors.dateTextColor,
                            }
                        },
                        toolbar: {
                            sx: {
                                backgroundColor: appTheme.colors.primary,
                                color: appTheme.colors.dateTextColor,
                            },
                        },
                        calendarHeader: {
                            sx: {
                                color: appTheme.colors.dateTextColor,
                            },
                        },
                        leftArrowIcon: {
                            sx: {
                                color: appTheme.colors.dateTextColor,
                            },
                        },
                        day: {
                            sx: {
                                color: appTheme.colors.dateTextColor,
                            },
                        },

                        nextIconButton: {
                            sx: {
                                color: appTheme.colors.dateTextColor,
                            },
                        },
                        previousIconButton: {
                            sx: {
                                color: appTheme.colors.dateTextColor,
                            },
                        },
                        rightArrowIcon: {
                            sx: {
                                color: appTheme.colors.dateTextColor,
                            },
                        },
                        field: {
                            sx: {
                                color: appTheme.colors.dateTextColor,
                            },
                        },
                        tabs: {
                            sx: {
                                color: appTheme.colors.dateTextColor,

                            },
                        },

                    }}
                    {...props}

                />
            </LocalizationProvider>
        </>
    )
}

export default AppDateTimePicker