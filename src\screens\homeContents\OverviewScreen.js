import { alpha, Box, Grid, Paper, useTheme } from '@mui/material'
import React, { useState } from 'react'
import UserPlaceHolderImage from "../../assets/images/user_image_place_holder.png"
import DefaultImage from "../../assets/images/default_image.jpg"
import KeyboardArrowRightRoundedIcon from '@mui/icons-material/KeyboardArrowRightRounded';
import CalendarMonthRoundedIcon from '@mui/icons-material/CalendarMonthRounded';
import QueryBuilderRoundedIcon from '@mui/icons-material/QueryBuilderRounded';
import MoreHorizRoundedIcon from '@mui/icons-material/MoreHorizRounded';
import { AppButton } from '../../components/ui_component/AppButton';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import HeatmapChart from '../../components/overviewchart/HeatmapChart';
import AudienceChart from '../../components/overviewchart/AudienceChart';
import FollowersGrowthChart from '../../components/overviewchart/FollowersGrowthChart';
import BackupRoundedIcon from '@mui/icons-material/BackupRounded';
import OverviewCountCard from '../../components/overviewchart/OverviewCountCard';


const OverviewScreen = () => {
    const [schedulePlaylistItems, setSchedulePlaylistItems] = useState([1, 2, 3])

    const theme = useTheme();
    const ScheduleCardItem = () => {
        return (
            <div className='schedulePlayList__item d-flex align-items-center '>
                <img src={DefaultImage} className='img-fluid schedulePlayList__item__image' alt='playlist Image' />
                <div className='d-flex flex-column ms-2 flex-grow-1 mt-1 justify-content-center'>
                    <div className='schedulePlayList__item__title'>Hi! What do you think about this?</div>
                    <div className='d-flex mt-2'>
                        <div className='d-flex align-items-center me-3'>
                            <div className='schedulePlayList__item__icon'>
                                <CalendarMonthRoundedIcon fontSize='small' />
                            </div>
                            <div className='schedulePlayList__item__duration'>May 17,2022</div>
                        </div>
                        <div className='d-flex align-items-center'>
                            <div className='schedulePlayList__item__icon'>
                                <QueryBuilderRoundedIcon fontSize='small' />
                            </div>
                            <div className='schedulePlayList__item__duration'>09.30</div>
                        </div>

                    </div>
                </div>
                <div className='ms-auto'>
                    <div className='schedulePlayList__item__threeDot'>
                        <MoreHorizRoundedIcon fontSize='small' />
                    </div>
                </div>
            </div>
        )
    }

    return (
        <>
            <Grid container>
                <Grid item xs={12} md={7} >
                    <div className='d-flex cardGapBottom'>
                        <div className='overviewCard d-flex flex-grow-1'>
                            <div className='d-flex  justify-content-center align-items-center'>
                                <img src={UserPlaceHolderImage} alt='user Image' className='img-fluid overViewCardImage' />
                                <div className='d-flex flex-column ms-3'>
                                    <div className='overviewCard__name'>Alexander Pato</div>
                                    <div className='overviewCard__handle'>@alexpato</div>
                                    <div className='d-flex mt-2 align-items-center'>
                                        <span className='overviewCard__viewPlaylist__text'>View Playlists</span>
                                        <span className='overviewCard__viewPlaylist__icon'>
                                            <KeyboardArrowRightRoundedIcon />
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className='cardGapLeft cardGapRight'>
                            <OverviewCountCard
                                heading="Followers" count="12,019"
                                icon={UserPlaceHolderImage} percentage="+ 0.2%"
                                percentageTime="This week" style={{ minWidth: "190px" }} />
                        </div>
                    </div>

                </Grid>
                <Grid item xs={12} md={5}>
                    <div className='d-flex justify-content-between cardGapBottom'>
                        <OverviewCountCard
                            heading="User Reach" count="3,914" percentageType="DOWN"
                            icon={UserPlaceHolderImage} percentage="+ 0.2%"
                            percentageTime="This week" className='flex-grow-1' />
                        <OverviewCountCard
                            heading="Account Views" count="105,124" percentageType="UP"
                            icon={UserPlaceHolderImage} percentage="+ 4.5%"
                            percentageTime="This week" className='flex-grow-1 cardGapLeft' />
                    </div>
                </Grid>
                <Grid item xs={12} md={7}>
                    <Paper elevation={3} sx={{ height: "100%" }} className='overviewCard d-flex flex-column flex-grow-1 cardGapBottom cardGapRight'>

                        <div className='d-flex CardHeader'>
                            <div className='CardHeader__title'>Follower Growth</div>
                            <div className='ms-auto d-flex'>
                                <div className='schedulePlayList__item__threeDot me-3'>
                                    <BackupRoundedIcon fontSize='small' />
                                </div>
                                <div className='schedulePlayList__item__threeDot'>
                                    <MoreHorizRoundedIcon fontSize='small' />
                                </div>
                            </div>
                        </div>
                        <Box sx={{ flexGrow: 1 }}>
                            <FollowersGrowthChart />
                        </Box>
                    </Paper>
                </Grid>
                <Grid item xs={12} md={5}>
                    <Paper elevation={3} sx={{ height: "100%" }} className='overviewCard d-flex flex-grow-1 cardGapBottom '>

                        <div className='schedulePlayListBox w-100'>
                            <div className='d-flex CardHeader'>
                                <div className='CardHeader__title'>Schedule Playlist</div>
                                <div className='ms-auto'>
                                    <AppButton label='Playlist' startIcon={<AddRoundedIcon />} />
                                </div>

                            </div>
                            <div className='schedulePlayList'>                                {
                                schedulePlaylistItems.map((obj, i) => {
                                    return <ScheduleCardItem key={i} />
                                })
                            }
                            </div>
                            <div className='d-flex justify-content-center align-items-center'>
                                <div className='d-flex mt-2 align-items-center'>
                                    <span className='overviewCard__viewPlaylist__text'>See All</span>
                                    <span className='overviewCard__viewPlaylist__icon'>
                                        <KeyboardArrowRightRoundedIcon />
                                    </span>
                                </div>
                            </div>
                        </div>

                    </Paper>
                </Grid>
                <Grid item xs={12} md={7} >
                    <Paper elevation={3} sx={{ height: "100%" }} className='overviewCard d-flex flex-column flex-grow-1 cardGapTop cardGapRight'>
                        <div className='d-flex CardHeader'>
                            <div className='CardHeader__title'>Global Engagement</div>

                            <div className='ms-auto'></div>
                        </div>
                        <Box sx={{ flexGrow: 1 }} className="mt-2">
                            <HeatmapChart />
                        </Box>
                    </Paper>
                </Grid>
                <Grid item xs={12} md={5}>
                    <Paper elevation={3} sx={{ height: "100%" }} className='overviewCard d-flex flex-column cardGapTop '>
                        <div className='d-flex CardHeader'>
                            <div className='CardHeader__title'>Audience Chart</div>

                            <div className='ms-auto'>
                                <div className='schedulePlayList__item__threeDot'>
                                    <MoreHorizRoundedIcon fontSize='small' />
                                </div>
                            </div>
                        </div>
                        <Box sx={{ flexGrow: 1 }}>
                            <AudienceChart />
                        </Box>
                    </Paper>
                </Grid>
            </Grid>
        </>
    )
}

export default OverviewScreen