import React from 'react'
import ArrowBackRoundedIcon from '@mui/icons-material/ArrowBackRounded';
import { IconButton } from '@mui/material';
import { useNavigate } from 'react-router-dom';
const BackButton = ({
    heading = "",

}) => {
    const navigate = useNavigate();

    return (
        <div className='d-flex align-items-center mb-4'>
            <div className='me-2' style={{ cursor: 'pointer' }}>
                <ArrowBackRoundedIcon fontSize='medium'
                    onClick={() => {
                        navigate(-1, { replace: true })
                    }} />

            </div>
            <div className='formBoxTitle mt-0'>{heading}</div>
        </div>
    )
}

export default BackButton