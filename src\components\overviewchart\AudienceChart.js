import React from 'react';
import { Bar<PERSON><PERSON> } from '@mui/x-charts/BarChart';
import { Box, Paper } from '@mui/material';

const AudienceChart = () => {
    const CustomToolTip = (props) => {
        const { itemData, series, } = props;
        // console.log("props", props)
        return (
            <Paper sx={{ backgroundColor: "#0A0A0C", borderRadius: "8px", padding: "4px 8px", paddingLeft: "16px" }}>
                <div className='lineChartTooltip__text'>{itemData.seriesId}</div>
                <div className='lineChartTooltip__text'>{series.data[itemData.dataIndex]}</div>
            </Paper>
        )
    }
    return (
        <>
            <div className="d-flex">
                <div className='d-flex me-2 align-items-center'>
                    <div className='lineChart__dot' style={{ backgroundColor: "#FC6767" }} />
                    <div className='lineChart__title'>Male</div>
                </div>
                <div className='d-flex me-2 align-items-center'>
                    <div className='lineChart__dot' style={{ backgroundColor: "#707070" }} />
                    <div className='lineChart__title'>Female</div>
                </div>

            </div>
            <BarChart
                xAxis={[{
                    scaleType: 'band',
                    data: ['0-17', '18-24', '25-34', "35-44", "45+"]
                }]}
                borderRadius={50}
                series={[{ data: [4, 3, 3, 7, 8], color: "#FC6767", id: "Male" },
                { data: [1, 6, 10, 20, 10], color: "#707070", id: "Female" }]}
                tooltip={{
                    trigger: "item", itemContent: CustomToolTip
                }}
                height={220}
                // yAxis={[{ scaleType: "linear", tickMaxStep: 20 }]}
                margin={{ left: 0, right: 0, top: 30, bottom: 30 }}
                grid={{ vertical: false, horizontal: false }}
                sx={{
                    display: "flex",
                    height: "100%",
                    paddingRight: "6px",
                    alignItems: "flex-start",
                    gap: "10px",
                    flexShrink: 0,
                    "& .MuiLineElement-root": {
                        strokeWidth: 2,
                    },
                    "& .MuiMarkElement-root": {
                        scale: "0.6",
                        fill: "#fff",
                        strokeWidth: 0,
                    },
                    //change left yAxis label styles
                    "& .MuiChartsAxis-left .MuiChartsAxis-tickLabel": {
                        fill: "#222222",
                        fontSize: "10px",
                        fontStyle: "normal",
                        lineHeight: "14px",
                        opacity: 0.5,
                    },

                    // change bottom label styles
                    "& .MuiChartsAxis-bottom .MuiChartsAxis-tickLabel": {
                        fill: "#222222",
                        fontSize: "10px",
                        fontStyle: "normal",
                        lineHeight: "14px",
                        opacity: 0.5,
                    },
                    // bottomAxis Line Styles
                    "& .MuiChartsAxis-bottom .MuiChartsAxis-line": {
                        stroke: "#222222",
                        strokeWidth: 0,
                    },
                    // leftAxis Line Styles
                    "& .MuiChartsAxis-left .MuiChartsAxis-line": {
                        stroke: "#222222",
                        strokeWidth: 0,
                    },
                    "& .MuiChartsAxisHighlight-root": {
                        strokeDasharray: 5,
                        strokeWidth: 0.6,
                    },
                    "& .MuiChartsGrid-horizontalLine": {
                        strokeDasharray: 5,
                        strokeWidth: 0.4,
                    },
                }}
            />
        </>
    )
}

export default AudienceChart