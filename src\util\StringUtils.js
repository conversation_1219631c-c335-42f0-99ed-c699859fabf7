export function PlaceholderForEmptyValue(value, placeholder = "-") {
    let return_value = placeholder
    if (value != undefined && value != null) {
        if (value.length != 0) {
            return_value = value;
        }
    }
    return return_value;
}
export function integerInputField(inputValue) {
    if (inputValue.length === 0) return true;
    const validInput = /^[0-9\b]+$/;
    if (!validInput.test(inputValue)) {
        return false;
    }
    return true;
}
export function decimalInputField(inputValue) {
    if (inputValue.length === 0) return true;
    const validInput = /^-?[0-9]\d*\.?\d*$/;
    if (!validInput.test(inputValue)) {
        return false;
    }
    return true;
}
export function ObjectKeyExist(objectData, keyVal) {
    if (objectData == null) return false;
    if (objectData.hasOwnProperty(keyVal)) {
        return true;
    }
    return false;
}
export function checkValueLength(value) {
    if (value != null && value.length != 0) {
        return true;
    }
    return false;
}
export function replaceNullToEmpty(value) {
    let convertedVal = value;
    if (value == undefined || value == null) {
        convertedVal = "";
    }
    return convertedVal;
}