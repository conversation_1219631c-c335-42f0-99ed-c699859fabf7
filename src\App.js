
import './App.css';
import {
  Routes,
  Route,
  HashRouter,
} from "react-router-dom";
import Login from './screens/Login';
import Home from './screens/Home';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { useDispatch, useSelector } from 'react-redux';
import { Alert, CssBaseline, Slide, Snackbar } from '@mui/material';
import { closeSnackBar } from './state/slice/snackbarSlice';
import useAppTheme from './ui_theme/useAppTheme';
import AppRoutes from './data/AppRoutes';
import AppLoader from './components/ui_component/AppLoader';
import { useEffect } from 'react';
import { closeLoader } from './state/slice/loaderSlice';
import PrivateRoute from './util/PrivateRoute';

function App() {
  const snackBar = useSelector((state) => state.snackbar.value);
  const dispatch = useDispatch();
  const pageLoading = useSelector((state) => state.appLoader.value.open);
  const appTheme = useAppTheme();
  useEffect(() => {
    dispatch(closeLoader());
  }, [])

  const theme = createTheme({
    palette: {
      primary: {
        main: appTheme.colors.primary,
        contrastText: '#fff',
      },
      secondary: {
        main: appTheme.colors.primary,
        contrastText: '#000',
      },
      text: {
        primary: appTheme.colors.textOnPrimary,
      },
      background: {
        default: appTheme.colors.background
      },
    },
    typography: {
      fontSize: appTheme.dimensions.defaultFontSize,
    },
    shadows: {
      0: appTheme.shadows[0],
      1: appTheme.shadows[1],
      2: appTheme.shadows[2],
      3: appTheme.shadows[3],
      4: appTheme.shadows[4],
      5: appTheme.shadows[5],
      6: appTheme.shadows[6],
      7: appTheme.shadows[7],
      8: appTheme.shadows[8],
      9: appTheme.shadows[9],
      10: appTheme.shadows[10],
      11: appTheme.shadows[11],
      12: appTheme.shadows[12],
      13: appTheme.shadows[13],
      14: appTheme.shadows[14],
      15: appTheme.shadows[15],
      16: appTheme.shadows[16],
      17: appTheme.shadows[17],
      18: appTheme.shadows[18],
      19: appTheme.shadows[19],
      20: appTheme.shadows[20],
      21: appTheme.shadows[21],
      22: appTheme.shadows[22],
      23: appTheme.shadows[23],
      24: appTheme.shadows[24],
    }
  });
  const handleSnackbarClose = () => {
    dispatch(closeSnackBar());
  }
  function SlideTransition(props) {
    return <Slide {...props} direction="down" />;
  }

  return (
    <>

      <ThemeProvider theme={theme}>
        <CssBaseline />
        <HashRouter basename="">
          <Routes>
            <Route path={`${AppRoutes.PLAYLIST_PATH}/:pageId`} element={<PrivateRoute />} />
            <Route path={`${AppRoutes.PLAYLIST_PATH}`} element={<PrivateRoute />} />
            <Route path={`${AppRoutes.LOGIN_PATH}`} element={<Login />} />
            <Route path="*" element={<Login />} />
          </Routes>
        </HashRouter>
      </ThemeProvider>
      <Snackbar
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
        open={snackBar.open}
        onClose={handleSnackbarClose}
        key={snackBar.id}
        autoHideDuration={8000}
        TransitionComponent={SlideTransition}>
        <Alert onClose={handleSnackbarClose} severity={snackBar.type == "SUCCESS" ? "success" : "error"}
          variant="filled"
          sx={{ width: '100%' }}>
          {snackBar.message}
        </Alert>
      </Snackbar>
      <AppLoader isLoading={pageLoading} />
    </>
  );
}

export default App;
