import { configureStore } from '@reduxjs/toolkit'
import snackbarSlice from './slice/snackbarSlice'
import drawerSlice from './slice/drawerSlice'
import loaderSlice from './slice/loaderSlice'
import loginSlice from './slice/loginSlice'

export default configureStore({
    reducer: {
        auth: loginSlice,
        snackbar: snackbarSlice,
        drawer: drawerSlice,
        appLoader: loaderSlice,
    },
})