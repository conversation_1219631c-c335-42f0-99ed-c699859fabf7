import { Avatar, AvatarGroup, Grid, Paper } from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  AppInput,
  AppTextareaInput,
} from "../../components/ui_component/AppInput";
import AppMediaSelection from "../../components/ui_component/AppMediaSelection";
import {
  AppSelect,
  DropdownButtonItem,
} from "../../components/ui_component/AppSelect";
import { AppSwitch } from "../../components/ui_component/AppSwitch";
import CurrencyRupeeRoundedIcon from "@mui/icons-material/CurrencyRupeeRounded";
import {
  AppButton,
  AppOutlineButton,
} from "../../components/ui_component/AppButton";
import UploadGuideComponent from "../../components/common/UploadGuideComponent";
import LastSubmittedComponent from "../../components/common/LastSubmittedComponent";
import AddRoundedIcon from "@mui/icons-material/AddRounded";
import ClipItemRow from "../../components/contentscreen/ClipItemRow";
import { useLocation, useNavigate } from "react-router-dom";
import AppRoutes from "../../data/AppRoutes";
import TagPeopleSelectionPopup from "../../components/contentscreen/TagPeopleSelectionPopup";
import PlaceholderImage from "../../assets/images/user_image_place_holder.png";
import { AppMultiSelect } from "../../components/ui_component/AppMultiSelect";
import { closeLoader, showLoader } from "../../state/slice/loaderSlice";
import { useDispatch } from "react-redux";
import {
  AddUpdateEpisodeApi,
  getEpisodeClipApi,
  getEpisodeDetailsApi,
  getLocationListApi,
  getSeasonListApi,
  getShowListApi,
  getUserTagListApi,
  getYearOfReleaseListApi,
  setEpisodeStatusApi,
} from "../../api/Apis";
import { checkValueLength, decimalInputField } from "../../util/StringUtils";
import { dateDbFormat, DBDateTimeFormat } from "../../util/DateUtils";
import { openSnackBar } from "../../state/slice/snackbarSlice";
import AppDatePicker from "../../components/ui_component/AppDatePicker";
import AppDateTimePicker from "../../components/ui_component/AppDateTimePicker";
import AppConfirmation from "../../components/ui_component/AppConfirmation";
import AppConstants from "../../data/AppConstants";
import BackButton from "../../components/common/BackButton";
import { _preventDoubleClick, getImageDimensions } from "../../util/util";
import { AppData } from "../../data/AppData";
import { getCurrentUserProfileSeq } from "../../storage/auth.login";
import AppVideoMediaSelection from "../../components/ui_component/AppVideoMediaSelection";
import LIT_ICON from "../../assets/images/reactions/Potty.png";
import MEH_ICON from "../../assets/images/reactions/Meh.png";
import Anger_icon from "../../assets/images/reactions/Anger.png";
import Audience_icon from "../../assets/images/reactions/Audience.png";
import Boo_icon from "../../assets/images/reactions/Boo.png";
import Bosslady_icon from "../../assets/images/reactions/Bosslady.png";
import BossMan_icon from "../../assets/images/reactions/BossMan.png";
import Celebration_icon from "../../assets/images/reactions/Celebration.png";
import Clap_icon from "../../assets/images/reactions/Clap.png";
import Disgust_icon from "../../assets/images/reactions/Disgust.png";
import Fear_icon from "../../assets/images/reactions/Fear.png";
import Surprise_icon from "../../assets/images/reactions/Surprise.png";
import Tease_icon from "../../assets/images/reactions/Tease.png";
import Timepass_icon from "../../assets/images/reactions/Timepass.png";
import Whatever_icon from "../../assets/images/reactions/Whatever.png";
import Yay_icon from "../../assets/images/reactions/Yay.png";
import Yum_icon from "../../assets/images/reactions/Yum.png";

let itemRowIndex = 1;
const AddEpisodeScreen = () => {
  const { state } = useLocation();
  console.log("state", state);
  const [pageEpisodeSeq, setPageEpisodeSeq] = useState(state.episodeSeq);
  useEffect(() => {
    if (state == undefined || !state.hasOwnProperty("episodeSeq")) {
      navigate(-1);
    } else {
      setPageEpisodeSeq(state.episodeSeq);
    }
  }, [state]);

  const [inputValues, setInputValues] = useState({
    showSeq: "",
    seasonSeq: "",
    title: "",
    description: "",
    priceValue: "",
    location: "",
    expiryDate: null,
    scheduleTime: null,
    yearOfRelease: "",
    gridTitle: "",

    showSeqErr: "",
    seasonSeqErr: "",
    titleErr: "",
    descriptionErr: "",
    priceValueErr: "",
    locationErr: "",
    expiryDateErr: "",
    scheduleTimeErr: "",
    yearOfReleaseErr: "",
    gridTitleErr: "",
  });
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [locationList, setLocationList] = useState([]);
  const [showList, setShowList] = useState([]);
  const [seasonList, setSeasonList] = useState([]);
  const [yearOfReleaseList, setYearOfReleaseList] = useState([]);

  const [isMonetize, setIsMonetize] = useState(false);

  const [openTagPeopleSelectionPopup, setOpenTagPeopleSelectionPopup] =
    useState(false);
  const [openTagPeopleSelectionPopupKey, setOpenTagPeopleSelectionPopupKey] =
    useState(Math.random());

  const [selectedThumbnailImage, setSelectedThumbnailImage] = useState(null);
  const [selectedThumbnailImageFile, setSelectedThumbnailImageFile] =
    useState(null);
  const [selectedThumbnailImageFileName, setSelectedThumbnailImageFileName] =
    useState("");
  const [selectedThumbnailImageErr, setSelectedThumbnailImageErr] =
    useState("");

  const [selectedPlaylistPreview, setSelectedPlaylistPreview] = useState(null);
  const [selectedPlaylistPreviewFile, setSelectedPlaylistPreviewFile] =
    useState(null);
  const [selectedPlaylistPreviewFileName, setSelectedPlaylistPreviewFileName] =
    useState(null);
  const [selectedPlaylistPreviewErr, setSelectedPlaylistPreviewErr] =
    useState("");

  const [selectedBannerFileImage, setSelectedBannerFileImage] = useState(null);
  const [selectedBannerImageFile, setSelectedBannerImageFile] = useState(null);
  const [selectedBannerImageFileName, setSelectedBannerImageFileName] =
    useState("");
  const [selectedBannerImageFileErr, setSelectedBannerImageFileErr] =
    useState("");

  const DropdownIcon = ({ icon = null }) => {
    return <img src={icon} className="dropdownIcon" alt="drop Down icon" />;
  };

  const [reactionList, setReactionList] = useState([
    {
      value: "ANGER",
      label: "Anger",
      icon: <DropdownIcon icon={Anger_icon} />,
    },
    // { value: "AUD", label: "Audience", icon: <DropdownIcon icon={Audience_icon} /> },
    { value: "BOO", label: "Boo", icon: <DropdownIcon icon={Boo_icon} /> },
    {
      value: "BLADY",
      label: "Bosslady",
      icon: <DropdownIcon icon={Bosslady_icon} />,
    },
    {
      value: "BMAN",
      label: "BossMan",
      icon: <DropdownIcon icon={BossMan_icon} />,
    },
    {
      value: "CELEB",
      label: "Celebration",
      icon: <DropdownIcon icon={Celebration_icon} />,
    },
    { value: "CLAP", label: "Clap", icon: <DropdownIcon icon={Clap_icon} /> },
    {
      value: "DISG",
      label: "Disgust",
      icon: <DropdownIcon icon={Disgust_icon} />,
    },
    { value: "FEAR", label: "Fear", icon: <DropdownIcon icon={Fear_icon} /> },
    { value: "MEH", label: "Meh", icon: <DropdownIcon icon={MEH_ICON} /> },
    { value: "LIT", label: "Potty", icon: <DropdownIcon icon={LIT_ICON} /> },
    {
      value: "SURP",
      label: "Surprise",
      icon: <DropdownIcon icon={Surprise_icon} />,
    },
    {
      value: "TEASE",
      label: "Tease",
      icon: <DropdownIcon icon={Tease_icon} />,
    },
    {
      value: "TPASS",
      label: "Timepass",
      icon: <DropdownIcon icon={Timepass_icon} />,
    },
    {
      value: "WEVER",
      label: "Whatever",
      icon: <DropdownIcon icon={Whatever_icon} />,
    },
    { value: "YAY", label: "Yay", icon: <DropdownIcon icon={Yay_icon} /> },
    { value: "YUM", label: "Yum", icon: <DropdownIcon icon={Yum_icon} /> },
  ]);
  const [selectedReactions, setSelectedReactions] = useState([]);
  const [selectedReactionsErr, setSelectedReactionsErr] = useState([]);
  const [dbFileData, setDbFileData] = useState({});
  const [selectedTagPeopleList, setSelectedTagPeopleList] = useState([]);

  const [confirmation, setConfirmation] = React.useState({
    open: false,
    title: "",
    message: "",
    onSubmit: null,
    positiveButton: "",
    negativeButton: "",
  });
  useEffect(() => {
    callEpisodeDetailsService();
    getLocationListService();
    getShowListService();
    getYearOfReleaseListService();
  }, []);
  const getLocationListService = () => {
    dispatch(showLoader());
    getLocationListApi().then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        let tempList = [];
        result.data.data.map((item) => {
          tempList.push({ label: item, value: item });
        });
        setLocationList(tempList);
      } else {
        setLocationList([]);
      }
    });
  };
  const getShowListService = () => {
    dispatch(showLoader());
    getShowListApi().then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        let tempList = [];
        result.data.data.map((obj) => {
          tempList.push({ label: obj.title, value: obj.show_seq });
        });
        setShowList(tempList);
      } else {
        setShowList([]);
      }
    });
  };
  const getSeasonListService = (showSeq) => {
    dispatch(showLoader());
    getSeasonListApi(showSeq).then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        let tempList = [];
        console.log("result.data", result.data);
        result.data.data.map((obj) => {
          tempList.push({ label: obj.title, value: obj.season_seq });
        });
        setSeasonList(tempList);
      } else {
        setSeasonList([]);
      }
    });
  };
  const callEpisodeDetailsService = () => {
    if (state == undefined) {
      navigate(-1);
    }
    if (checkValueLength(pageEpisodeSeq) && pageEpisodeSeq != -1) {
      dispatch(showLoader());
      getEpisodeDetailsApi(pageEpisodeSeq).then((result) => {
        dispatch(closeLoader());
        if (result.isSuccess) {
          getEpisodeClipData(pageEpisodeSeq);

          const dbData = result.data.data[0];
          let selSeason = "";
          if (checkValueLength(dbData.season_seq) && dbData.season_seq != -1) {
            selSeason = dbData.season_seq;
          }
          let data = {
            title: dbData.title,
            description: dbData.description,
            gridTitle: checkValueLength(dbData.grid_title)
              ? dbData.grid_title
              : "",
            priceValue: checkValueLength(dbData.price) ? dbData.price : "",
            location: checkValueLength(dbData.location) ? dbData.location : "",
            expiryDate: checkValueLength(dbData.expiry_date)
              ? new Date(dbData.expiry_date)
              : null,
            scheduleTime: checkValueLength(dbData.schedule)
              ? new Date(dbData.schedule)
              : null,
            showSeq: dbData.show_seq,
            seasonSeq: selSeason,
            yearOfRelease: checkValueLength(dbData.release_year)
              ? dbData.release_year
              : "",
          };
          getSeasonListService(dbData.show_seq);
          let reactions = [];
          dbData.reactions.map((item) => {
            reactions.push(item[0]);
          });
          setSelectedReactions(reactions);
          let people_tags = [];
          dbData.tags.map((obj) => {
            people_tags.push({
              profile_seq: obj.profile_seq,
              user_seq: -1,
              display_name: "",
              user_handle: "",
              profile_image: obj.profile_picture,
              isChecked: true,
            });
          });
          setSelectedTagPeopleList(people_tags);
          let dataFile = {
            thumb_file: dbData.thumb_file,
            thumb_file_name: "show thumb",
            preview_file: dbData.preview_file,
            preview_file_name: "show preview",
            banner_file: dbData.banner_file,
            banner_file_name: "show banner image",
          };
          let isPaid = false;
          if (dbData.is_paid == "YES") {
            isPaid = true;
          }
          setIsMonetize(isPaid);
          setDbFileData(dataFile);

          setSelectedThumbnailImageFile(dataFile.thumb_file);
          setSelectedPlaylistPreviewFile(dataFile.preview_file);

          setSelectedThumbnailImageFileName(dataFile.thumb_file_name);
          setSelectedPlaylistPreviewFileName(dataFile.preview_file_name);

          setSelectedBannerImageFile(dataFile.banner_file);
          setSelectedBannerImageFileName(dataFile.banner_file_name);
          setInputValues((prevState) => ({
            ...prevState,
            ...data,
          }));
        } else {
          dispatch(
            openSnackBar({
              open: true,
              message: result.message,
              type: "FAILED",
            })
          );
        }
      });
    }
  };
  const handleInputChange = (name) => (value) => {
    if (name == "expiryDate" || name == "scheduleTime") {
      setInputValues((prevState) => ({
        ...prevState,
        [name]: value,
        [name + "Err"]: "",
      }));
    } else if (name == "priceValue") {
      if (decimalInputField(value)) {
        setInputValues((prevState) => ({
          ...prevState,
          [name]: value,
          [name + "Err"]: "",
        }));
      }
    } else {
      setInputValues((prevState) => ({
        ...prevState,
        [name]: value,
        [name + "Err"]: "",
      }));
    }
    if (name == "showSeq") {
      getSeasonListService(value);
    }
  };
  const handleMonetize = () => {
    setIsMonetize(!isMonetize);
  };
  const [lastSubmittedData, setLastSubmittedData] = useState([
    {
      id: 1,
      title: "How to Design a Logotype",
      time: "Aug 21, 2021",
      type: "PUBLISHED",
    },
    {
      id: 2,
      title: "Adobe Illustrator Masterclass",
      time: "Aug 17, 2021",
      type: "PUBLISHED",
    },
    {
      id: 3,
      title: "Adobe Illustrator Masterclass",
      time: "Aug 17, 2021",
      type: "PUBLISHED",
    },
  ]);
  const thumbnailImageClick = async (clickID, obj) => {
    setSelectedThumbnailImageErr("");
    if (clickID == "SELECT_MEDIA") {
      if (obj.mediaData.length != 0) {
        let mediaData = obj.mediaData[0];
        const dimensions = await getImageDimensions(mediaData);
        if (validateThumbnailImage(mediaData.type)) {
          if (mediaData.size <= AppConstants.DEFAULT_UPLOAD_IMAGE_SIZE) {
            setSelectedThumbnailImage(mediaData);
            setSelectedThumbnailImageFile(URL.createObjectURL(mediaData));
            setSelectedThumbnailImageFileName(mediaData.name);
          } else {
            setSelectedThumbnailImageErr("Image size should be less than 5MB");
          }
        } else {
          setSelectedThumbnailImageErr("file format should be jpeg/png/jpg");
        }
      }
    } else if (clickID == "DELETE_MEDIA") {
      setSelectedThumbnailImage(null);
      if (dbFileData.hasOwnProperty("thumb_file")) {
        setSelectedThumbnailImageFile(dbFileData.thumb_file);
        setSelectedThumbnailImageFileName(dbFileData.thumb_file_name);
      } else {
        setSelectedThumbnailImageFile(null);
        setSelectedThumbnailImageFileName("");
      }
    }
  };
  const playlistPreviewClick = (clickID, obj) => {
    setSelectedPlaylistPreviewErr("");
    if (clickID == "SELECT_MEDIA") {
      if (obj.mediaData.length != 0) {
        let mediaData = obj.mediaData[0];
        const fileMaxSize =
          AppConstants.DEFAULT_UPLOAD_PREVIEW_VIDEO_SIZE * 1024 * 1000;
        if (validateVideoFile(mediaData.type)) {
          if (mediaData.size <= fileMaxSize) {
            setSelectedPlaylistPreview(mediaData);
            setSelectedPlaylistPreviewFile(URL.createObjectURL(mediaData));
            setSelectedPlaylistPreviewFileName(mediaData.name);
          } else {
            setSelectedPlaylistPreviewErr(
              `video size should be less than ${AppConstants.DEFAULT_UPLOAD_PREVIEW_VIDEO_SIZE}MB`
            );
          }
        } else {
          setSelectedPlaylistPreviewErr("file format should be mp4");
        }
      }
    } else if (clickID == "DELETE_MEDIA") {
      setSelectedPlaylistPreview(null);
      if (dbFileData.hasOwnProperty("preview_file")) {
        setSelectedPlaylistPreviewFile(dbFileData.preview_file);
        setSelectedPlaylistPreviewFileName(dbFileData.preview_file_name);
      } else {
        setSelectedPlaylistPreviewFile(null);
        setSelectedPlaylistPreviewFileName("");
      }
    }
  };
  const bannerImageClick = (clickID, obj) => {
    setSelectedBannerImageFileErr("");
    if (clickID == "SELECT_MEDIA") {
      if (obj.mediaData.length != 0) {
        let mediaData = obj.mediaData[0];
        if (validateThumbnailImage(mediaData.type)) {
          if (mediaData.size <= AppConstants.DEFAULT_UPLOAD_IMAGE_SIZE) {
            setSelectedBannerFileImage(mediaData);
            setSelectedBannerImageFile(URL.createObjectURL(mediaData));
            setSelectedBannerImageFileName(mediaData.name);
          } else {
            setSelectedBannerImageFileErr("Image size should be less than 5MB");
          }
        } else {
          setSelectedBannerImageFileErr("file format should be jpeg/png/jpg");
        }
      }
    } else if (clickID == "DELETE_MEDIA") {
      setSelectedBannerFileImage(null);
      if (dbFileData.hasOwnProperty("banner_file")) {
        setSelectedBannerImageFile(dbFileData.banner_file);
        setSelectedBannerImageFileName(dbFileData.banner_file_name);
      } else {
        setSelectedBannerImageFile(null);
        setSelectedBannerImageFileName("");
      }
    }
  };
  const validateThumbnailImage = (mediaType) => {
    let validMedia = false;
    if (
      mediaType === "image/png" ||
      mediaType === "image/jpg" ||
      mediaType === "image/jpeg"
    ) {
      validMedia = true;
    }
    return validMedia;
  };
  const validateVideoFile = (mediaType) => {
    let validMedia = false;
    if (mediaType === "video/mp4") {
      validMedia = true;
    }
    return validMedia;
  };
  const addNewShowBtnPress = () => {
    navigate(AppRoutes.ADD_SHOW_PATH, { state: { showSeq: -1 } });
  };
  const addNewSeasonBtnPress = () => {
    setConfirmation({
      open: true,
      title: "Confirmation",
      message:
        "Do you want to add a new season? Adding a new season will disallow addition of new episodes to the current season. Episodes can only be added to the new season.",
      positiveButton: "Confirm",
      negativeButton: "Cancel",
      onSubmit: () => {
        navigate(AppRoutes.ADD_SEASON_PATH, {
          state: { seasonSeq: -1, showSeq: inputValues.showSeq },
        });
      },
    });
  };
  const [clipItemList, setClipItemList] = useState([]);

  const openTagPeoplePopup = () => {
    setOpenTagPeopleSelectionPopup(true);
    setOpenTagPeopleSelectionPopupKey(Math.random());
  };

  const handleReactionSelection = (value) => {
    if (value.length <= 2) {
      setSelectedReactions(value);
      setSelectedReactionsErr("");
    } else {
      dispatch(
        openSnackBar({
          open: true,
          message: "Selection of maximum reaction 2",
          type: "FAILED",
          id: Math.random(),
        })
      );
    }
  };

  const formValid = () => {
    let isValid = true;
    let errorData = {
      showSeqErr: "",
      seasonSeqErr: "",
      titleErr: "",
      descriptionErr: "",
      priceValueErr: "",
      locationErr: "",
      expiryDateErr: "",
      scheduleTimeErr: "",
      yearOfReleaseErr: "",
      gridTitleErr: "",
    };
    if (pageEpisodeSeq == -1) {
      if (selectedThumbnailImage == null) {
        setSelectedThumbnailImageErr("Please add the Thumbnail Image");
        isValid = false;
      }
      if (selectedBannerFileImage == null) {
        setSelectedBannerImageFileErr("Please add the banner Image");
        isValid = false;
      }
      // if (selectedPlaylistPreview == null) {
      //     setSelectedPlaylistPreviewErr("Please add the Playlist Preview");
      //     isValid = false;
      // }
    }
    if (!checkValueLength(inputValues.showSeq)) {
      errorData.showSeqErr = "Please select the show";
      isValid = false;
    }
    if (checkValueLength(inputValues.showSeq)) {
      if (seasonList.length > 0 && !checkValueLength(inputValues.seasonSeq)) {
        errorData.seasonSeqErr = "Please select the season";
        isValid = false;
      }
    }
    if (!checkValueLength(inputValues.title)) {
      errorData.titleErr = "Title is required";
      isValid = false;
    }
    if (!checkValueLength(inputValues.gridTitle)) {
      errorData.gridTitleErr = "Grid Title is required";
      isValid = false;
    }
    if (isMonetize) {
      if (!checkValueLength(inputValues.priceValue)) {
        errorData.priceValueErr = "Price is required";
        isValid = false;
      }
    }
    if (!checkValueLength(inputValues.description)) {
      errorData.descriptionErr = "Description is required";
      isValid = false;
    }
    setInputValues((prevState) => ({
      ...prevState,
      ...errorData,
    }));
    return isValid;
  };
  const saveBtnPress = () => {
    if (formValid()) {
      if (_preventDoubleClick(AppData.preventTime)) {
        AppData.preventTime = new Date();
        // console.log("sds")
        dispatch(showLoader());
        submitEpisodeData();
      }
    }
  };
  const submitEpisodeData = () => {
    let is_paid = "NO";
    if (isMonetize) {
      is_paid = "YES";
    }
    let imageHasMap = [];
    if (selectedThumbnailImage != null) {
      imageHasMap.push({
        inputName: "thumb_file",
        imageData: selectedThumbnailImage,
      });
    }
    if (selectedPlaylistPreview != null) {
      imageHasMap.push({
        inputName: "preview_file",
        imageData: selectedPlaylistPreview,
      });
    }
    if (selectedBannerFileImage != null) {
      imageHasMap.push({
        inputName: "banner_file",
        imageData: selectedBannerFileImage,
      });
    }
    let priceValue = "";
    if (isMonetize) {
      priceValue = inputValues.priceValue;
    }
    let expiryDate = "";
    if (checkValueLength(inputValues.expiryDate)) {
      expiryDate = encodeURIComponent(dateDbFormat(inputValues.expiryDate));
    }
    let scheduleOn = "";
    if (checkValueLength(inputValues.scheduleTime)) {
      scheduleOn = encodeURIComponent(
        DBDateTimeFormat(inputValues.scheduleTime)
      );
    }
    let locationValue = "";
    if (checkValueLength(inputValues.location)) {
      locationValue = encodeURIComponent(inputValues.location);
    }
    let titleVal = encodeURIComponent(inputValues.title);
    let gridTitle = encodeURIComponent(inputValues.gridTitle);
    let descriptionVal = encodeURIComponent(inputValues.description);
    let episodeSeq = pageEpisodeSeq;
    let reactions = [];
    if (selectedReactions.length != 0) {
      reactions = encodeURIComponent(JSON.stringify(selectedReactions));
    }
    let people_tags = [];
    if (selectedTagPeopleList.length !== 0) {
      selectedTagPeopleList.forEach((item) => {
        people_tags.push(item.profile_seq);
      });
      people_tags = encodeURIComponent(JSON.stringify(people_tags));
    }
    let yearOfRelease = "";
    if (checkValueLength(inputValues.yearOfRelease)) {
      yearOfRelease = inputValues.yearOfRelease;
    }
    AddUpdateEpisodeApi(
      episodeSeq,
      inputValues.showSeq,
      inputValues.seasonSeq,
      titleVal,
      gridTitle,
      descriptionVal,
      is_paid,
      priceValue,
      expiryDate,
      scheduleOn,
      locationValue,
      reactions,
      people_tags,
      yearOfRelease,
      imageHasMap
    ).then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "SUCCESS",
          })
        );
        if (pageEpisodeSeq == -1) {
          setPageEpisodeSeq(parseInt(result.data.data.episode_seq));
        }
        // if (episodeSeq == -1) {
        //     navigate(-1)
        // }
      } else {
        let errorMsgShow = false;
        if (result.data && result.data.err_cd == "E006" && result.data.data) {
          let errorMsgData = {
            showSeqErr: "",
            seasonSeqErr: "",
            titleErr: "",
            descriptionErr: "",
            priceValueErr: "",
            locationErr: "",
            expiryDateErr: "",
            scheduleTimeErr: "",
            yearOfReleaseErr: "",
            gridTitleErr: "",
          };
          if (result.data.data.show_seq) {
            errorMsgData.showSeqErr = result.data.data.show_seq;
            errorMsgShow = true;
          }
          if (result.data.data.season_seq) {
            errorMsgData.seasonSeqErr = result.data.data.season_seq;
            errorMsgShow = true;
          }
          if (result.data.data.title) {
            errorMsgData.titleErr = result.data.data.title;
            errorMsgShow = true;
          }
          if (result.data.data.description) {
            errorMsgData.descriptionErr = result.data.data.description;
            errorMsgShow = true;
          }
          if (result.data.data.price) {
            errorMsgData.priceValueErr = result.data.data.price;
            errorMsgShow = true;
          }
          if (result.data.data.location) {
            errorMsgData.locationErr = result.data.data.location;
            errorMsgShow = true;
          }
          if (result.data.data.expiry_date) {
            errorMsgData.expiryDateErr = result.data.data.expiry_date;
            errorMsgShow = true;
          }
          if (result.data.data.schedule_on) {
            errorMsgData.scheduleTimeErr = result.data.data.schedule_on;
            errorMsgShow = true;
          }
          if (result.data.data.release_year) {
            errorMsgData.yearOfReleaseErr = result.data.data.release_year;
            errorMsgShow = true;
          }
          if (result.data.data.grid_title) {
            errorMsgData.gridTitle = result.data.data.grid_title;
            errorMsgShow = true;
          }
          if (result.data.data.is_paid) {
            dispatch(
              openSnackBar({
                open: true,
                message: result.data.data.is_paid,
                type: "FAILED",
              })
            );
            errorMsgShow = true;
          }
          setInputValues((prevState) => ({
            ...prevState,
            ...errorMsgData,
          }));
        }
        if (!errorMsgShow) {
          dispatch(
            openSnackBar({
              open: true,
              message: result.message,
              type: "FAILED",
            })
          );
        }
      }
    });
  };
  const handleConfirmationClose = () => {
    setConfirmation((prevState) => ({
      ...prevState,
      open: false,
    }));
  };
  const deleteBtnPress = () => {
    setConfirmation({
      open: true,
      title: "Confirmation",
      message: "Do you want to delete this episode?",
      positiveButton: "Yes",
      negativeButton: "No",
      onSubmit: () => {
        callEpisodeDeleteService(pageEpisodeSeq);
      },
    });
  };
  const callEpisodeDeleteService = (episodeSeq) => {
    dispatch(showLoader());
    setEpisodeStatusApi(episodeSeq, "INACTIVE").then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "SUCCESS",
          })
        );
        navigate(-1);
      } else {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "FAILED",
          })
        );
      }
    });
  };
  const closeTagPeoplePopup = () => {
    setOpenTagPeopleSelectionPopup(false);
  };
  const onTagPeople = (clickID, obj) => {
    if (clickID == "SUBMIT") {
      setOpenTagPeopleSelectionPopup(false);
      setSelectedTagPeopleList([...[], ...obj.data]);
    }
  };
  const addClipBtnPress = () => {
    const clipItemObj = {
      indexID: itemRowIndex,
      clipSeq: -1,
      clipTitle: "",
      clipFile: null,
      clipDisplayFile: null,
      clipFileName: null,
    };
    itemRowIndex += 1;
    setClipItemList([...clipItemList, clipItemObj]);
  };
  const clipRowBtnPress = (clickID, obj) => {
    if (clickID == "DELETE_ROW") {
      let tempList = JSON.parse(JSON.stringify(clipItemList));
      tempList.splice(obj.index, 1);
      setClipItemList([...[], ...tempList]);
    } else if (clickID == "DELETE_CLIP") {
      let tempList = JSON.parse(JSON.stringify(clipItemList));
      tempList.splice(obj.index, 1);
      setClipItemList([...[], ...tempList]);
    }
  };
  const getEpisodeClipData = (episodeSeq) => {
    dispatch(showLoader());
    getEpisodeClipApi(episodeSeq).then((result) => {
      // console.log("Result", result)
      dispatch(closeLoader());
      if (result.isSuccess) {
        let prevData = [];
        result.data.data.map((obj) => {
          let clipItemObj = {
            indexID: itemRowIndex,
            clipSeq: obj.post_seq,
            clipTitle: obj.post_comments,
            clipFile: null,
            clipDisplayFile: null,
            clipFileName: "",
          };
          prevData.push(clipItemObj);
          itemRowIndex += 1;
        });
        setClipItemList([...[], ...prevData]);
      } else {
        setClipItemList([]);
      }
    });
  };
  const getYearOfReleaseListService = () => {
    dispatch(showLoader());
    getYearOfReleaseListApi().then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        let tempList = [];
        result.data.data.map((item) => {
          tempList.push({ label: item, value: item });
        });
        setYearOfReleaseList(tempList);
      } else {
        setYearOfReleaseList([]);
      }
    });
  };
  return (
    <>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <BackButton
            heading={
              pageEpisodeSeq == -1 ? "Upload New Episode" : "Update Episode"
            }
          />
        </Grid>
      </Grid>
      <Grid container spacing={2}>
        <Grid item xs={12} md={8}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <AppInput
                label="Title"
                placeholder="Type here"
                value={inputValues.title}
                onTextChange={handleInputChange("title")}
                maxLength={50}
                errorMessage={inputValues.titleErr}
                marginBottom="18px"
              />
              <AppInput
                label="Grid Title"
                placeholder="Type here"
                value={inputValues.gridTitle}
                onTextChange={handleInputChange("gridTitle")}
                maxLength={24}
                errorMessage={inputValues.gridTitleErr}
                marginBottom="18px"
              />
              <AppTextareaInput
                multiline
                minRows={3}
                maxRows={3}
                label="Description"
                placeholder="Type here"
                maxLength={340}
                value={inputValues.description}
                errorMessage={inputValues.descriptionErr}
                onTextChange={handleInputChange("description")}
                marginBottom="18px"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <AppMultiSelect
                    label="Reactions"
                    placeholder="Please Select"
                    options={reactionList}
                    value={selectedReactions}
                    onSelectChange={handleReactionSelection}
                    marginBottom="16px"
                    errorMessage={selectedReactionsErr}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <AppInput
                    label={"People"}
                    placeholder={"Select People"}
                    value={
                      selectedTagPeopleList.length > 0
                        ? `${selectedTagPeopleList.length} selected`
                        : ""
                    }
                    readOnly={true}
                    onClick={(e) => openTagPeoplePopup()}
                    marginBottom="22px"
                    style={{ cursor: "pointer" }}
                  />
                  {selectedTagPeopleList.length > 0 ? (
                    <div className="d-flex">
                      <AvatarGroup max={4} spacing={"small"}>
                        {selectedTagPeopleList.map((obj, i) => {
                          return (
                            <Avatar
                              key={i}
                              src={
                                obj.profile_image != null
                                  ? obj.profile_image
                                  : PlaceholderImage
                              }
                              alt={obj.display_name}
                            />
                          );
                        })}
                      </AvatarGroup>
                    </div>
                  ) : null}
                </Grid>
                <Grid item xs={12} md={6}>
                  <AppSelect
                    label="Show"
                    placeholder="Please Select"
                    options={showList}
                    value={inputValues.showSeq}
                    onSelectChange={handleInputChange("showSeq")}
                    marginBottom="8px"
                    errorMessage={inputValues.showSeqErr}
                    menuItemButton={
                      <DropdownButtonItem
                        buttonLabel="Add New Show"
                        buttonPress={() => addNewShowBtnPress()}
                      />
                    }
                  />
                  <div className="inputExtraText">
                    You need to add show details to upload your episode
                  </div>
                </Grid>
                <Grid item xs={12} md={6}>
                  <AppSelect
                    label="Season"
                    placeholder="Please Select"
                    options={seasonList}
                    value={inputValues.seasonSeq}
                    onSelectChange={handleInputChange("seasonSeq")}
                    marginBottom="8px"
                    errorMessage={inputValues.seasonSeqErr}
                    menuItemButton={
                      checkValueLength(inputValues.showSeq) ? (
                        <DropdownButtonItem
                          buttonLabel="Add New Season"
                          buttonPress={() => addNewSeasonBtnPress()}
                        />
                      ) : null
                    }
                  />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} md={6}>
              <AppMediaSelection
                headings="Upload Thumbnail Image"
                secondHeading="JPG file, 1080 x 1920 px"
                selectedMedia={selectedThumbnailImage}
                onMediaChange={thumbnailImageClick}
                displayMediaFile={selectedThumbnailImageFile}
                mediaFileName={selectedThumbnailImageFileName}
                mediaErr={selectedThumbnailImageErr}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <AppVideoMediaSelection
                headings="Upload Playlist Preview"
                secondHeading="MP4 file of upto 10 seconds"
                selectedMedia={selectedPlaylistPreview}
                onMediaChange={playlistPreviewClick}
                displayMediaFile={selectedPlaylistPreviewFile}
                mediaFileName={selectedPlaylistPreviewFileName}
                mediaErr={selectedPlaylistPreviewErr}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <AppMediaSelection
                headings="Upload Banner image"
                secondHeading="JPG file, 1920 x 1080 px"
                selectedMedia={selectedBannerFileImage}
                onMediaChange={bannerImageClick}
                displayMediaFile={selectedBannerImageFile}
                mediaFileName={selectedBannerImageFileName}
                mediaErr={selectedBannerImageFileErr}
              />
            </Grid>

            {/* <Grid item xs={12} md={12} mt={2}>
                            <div className='d-flex align-items-center'>
                                <div className='monetizeText me-3'>Monetize</div>
                                {
                                    pageEpisodeSeq == -1 ?
                                        <AppSwitch checked={isMonetize} onChange={() => handleMonetize()} />
                                        : null
                                }
                            </div>
                        </Grid> */}
            <Grid item xs={12} mt={2}>
              <Grid container spacing={2}>
                {/* <Grid item xs={12} md={3}>
                                    <AppInput
                                        label='Price'
                                        placeholder='Type here'
                                        value={inputValues.priceValue}
                                        errorMessage={inputValues.priceValueErr}
                                        onTextChange={handleInputChange('priceValue')}
                                        marginBottom='18px'
                                        disabled={!isMonetize}
                                        startIcon={<CurrencyRupeeRoundedIcon fontSize='small' />} />
                                </Grid> */}
                <Grid item xs={12} md={3}>
                  <AppSelect
                    label="Year of Release"
                    placeholder="Please Select"
                    options={yearOfReleaseList}
                    errorMessage={inputValues.yearOfReleaseErr}
                    value={inputValues.yearOfRelease}
                    onSelectChange={handleInputChange("yearOfRelease")}
                    marginBottom="18px"
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <AppSelect
                    label="Location"
                    placeholder="Please Select"
                    options={locationList}
                    errorMessage={inputValues.locationErr}
                    value={inputValues.location}
                    onSelectChange={handleInputChange("location")}
                    marginBottom="18px"
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <AppDatePicker
                    label="Expiry Date"
                    placeholder="Type here"
                    marginBottom="18px"
                    value={inputValues.expiryDate}
                    errorMessage={inputValues.expiryDateErr}
                    onDateChange={handleInputChange("expiryDate")}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <AppDateTimePicker
                    label="Schedule"
                    placeholder="Type here"
                    marginBottom="18px"
                    value={inputValues.scheduleTime}
                    errorMessage={inputValues.scheduleTimeErr}
                    onDateChange={handleInputChange("scheduleTime")}
                  />
                </Grid>
                <Grid item xs={12}></Grid>

                {pageEpisodeSeq == -1 ? (
                  <>
                    {/* <AppOutlineButton label='Preview' onClick={() => console.log("Preview Press")} /> */}
                  </>
                ) : (
                  <Grid item xs={12} md={4} mb={3}>
                    <AppOutlineButton
                      label="Delete"
                      onClick={() => deleteBtnPress()}
                    />
                  </Grid>
                )}

                <Grid item xs={12} md={8} mb={3}>
                  <AppButton
                    label={`${pageEpisodeSeq == -1 ? "Save" : "Update"}`}
                    onClick={() => saveBtnPress()}
                  />
                </Grid>
              </Grid>
            </Grid>
            {pageEpisodeSeq !== -1 ? (
              <>
                <Grid item xs={12} md={12}>
                  <div className="formBoxTitle">Clips</div>
                </Grid>
                <Grid item xs={12} md={12}>
                  {clipItemList.map((obj, i) => {
                    return (
                      <ClipItemRow
                        key={obj.indexID}
                        data={obj}
                        index={i}
                        clipRowBtnPress={clipRowBtnPress}
                        episodeSeq={pageEpisodeSeq}
                      />
                    );
                  })}
                </Grid>
                <Grid item xs={12} md={12}>
                  <AppButton
                    className="me-2 px-4"
                    label="Clip"
                    startIcon={<AddRoundedIcon />}
                    onClick={() => addClipBtnPress()}
                    fullWidth={false}
                  />
                </Grid>
              </>
            ) : null}
          </Grid>
        </Grid>
        <Grid item xs={12} md={4}>
          <UploadGuideComponent />
          <div className="mt-5">
            <LastSubmittedComponent data={lastSubmittedData} />
          </div>
        </Grid>
      </Grid>
      {openTagPeopleSelectionPopup ? (
        <TagPeopleSelectionPopup
          open={openTagPeopleSelectionPopup}
          key={openTagPeopleSelectionPopupKey}
          selectedTagPeople={selectedTagPeopleList}
          onClose={closeTagPeoplePopup}
          onTagPeople={onTagPeople}
        />
      ) : null}
      <AppConfirmation
        open={confirmation.open}
        onClose={handleConfirmationClose}
        onSubmit={confirmation.onSubmit}
        title={confirmation.title}
        message={confirmation.message}
        positiveButton={confirmation.positiveButton}
        negativeButton={confirmation.negativeButton}
      />
    </>
  );
};

export default AddEpisodeScreen;
