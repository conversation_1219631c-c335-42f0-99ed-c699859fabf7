import { createSlice } from '@reduxjs/toolkit'

export const snackbarSlice = createSlice({
    name: 'snackbarSlice',
    initialState: {
        value: { open: false, message: "", id: "" + Math.random(), type: "FAILED" },
    },
    reducers: {
        openSnackBar: (state, action) => {
            state.value.open = true;
            state.value.message = action.payload.message;
            state.value.id = action.payload.id;
            state.value.type = action.payload.type;
        },
        closeSnackBar: (state) => {
            state.value.open = false;
        },
    },
});

// Action creators are generated for each case reducer function
export const { openSnackBar, closeSnackBar } = snackbarSlice.actions

export default snackbarSlice.reducer
