import {
  getAccess<PERSON>ey,
  getUserCredential,
  setAccessKey,
} from "../storage/auth.login";

class ServerConnector {
  constructor() {}
  async postData(json_data) {
    json_data["_access_key"] = getAccessKey();
    let userDetails = getUserCredential();
    if (userDetails !== null) {
      if (!json_data.hasOwnProperty("user_id")) {
        if (userDetails._user_id.length !== 0) {
          json_data["user_id"] = userDetails._user_id;
        }
      }
      if (!json_data.hasOwnProperty("password")) {
        if (userDetails._password.length !== 0) {
          json_data["password"] = userDetails._password;
        }
      }
    }
    const requestOptions = {
      method: "POST",
      headers: {
        // 'Content-Type': 'application/json'
      },
      body: JSON.stringify(json_data),
    };
    const urlPath = getUrlPath(json_data._action_code);
    consoleData(urlPath);
    consoleData(requestOptions);
    try {
      const response = await fetch(urlPath, requestOptions);
      if (response.status == 500) {
        consoleData(json_data._action_code, response);
        return getResultFormat(
          "There is an issue detected at the server side."
        );
      } else if (response.status != 200) {
        consoleData(json_data._action_code, response);
        return getResultFormat(
          "Could not connect to the server. Please try again later."
        );
      }
      try {
        const resultData = await response.json();
        consoleData(json_data._action_code, resultData);
        if (resultData._access_key && resultData._access_key.length > 0) {
          setAccessKey(resultData._access_key);
        }
        return getResultFormat(resultData.msg, resultData.status, resultData);
      } catch (error) {
        return getResultFormat("Data parse error.");
      }
    } catch (error) {
      if (error == "TypeError: Failed to fetch") {
        error = "Could not connect to the server. Please try again";
      } else if (
        error == "TypeError: Network request failed" ||
        error == "[TypeError: Network request failed]"
      ) {
        error =
          "Could not connect to the server due to network issue. Please check your network connection";
      } else {
        error = "Could not reach server. (" + error + ")";
      }
      consoleData(json_data._action_code, error);
      return getResultFormat(error);
    }
  }
  async postDataMultiPart(dataHashMap, imageHasMap) {
    dataHashMap["_access_key"] = getAccessKey();
    let userDetails = getUserCredential();
    if (userDetails !== null) {
      if (!dataHashMap.hasOwnProperty("user_id")) {
        if (userDetails._user_id.length !== 0) {
          dataHashMap["user_id"] = encodeURIComponent(userDetails._user_id);
        }
      }
      if (!dataHashMap.hasOwnProperty("password")) {
        if (userDetails._password.length !== 0) {
          dataHashMap["password"] = encodeURIComponent(userDetails._password);
        }
      }
    }
    let formData = new FormData();
    let index = 0;
    for (let key in imageHasMap) {
      formData.append(
        imageHasMap[key].inputName,
        imageHasMap[key].imageData,
        imageHasMap[key].imageData.name
      );
      index++;
    }
    let isFormDataEmpty = true;
    for (let p of formData) {
      isFormDataEmpty = false;
      break;
    }
    let parameters = "";
    for (let key in dataHashMap) {
      if (parameters.length != 0) {
        parameters += "&";
      }
      parameters += key + "=" + dataHashMap[key];
    }
    const requestOptions = {
      method: "POST",
    };
    // Body Changes For formdata is Empty or not
    if (isFormDataEmpty) {
      requestOptions.body = null;
    } else {
      requestOptions.body = formData;
    }
    const urlPath = getUrlPath(dataHashMap._action_code);
    consoleData(urlPath + "?" + parameters);
    consoleData(dataHashMap._action_code, requestOptions);
    try {
      const response = await fetch(urlPath + "?" + parameters, requestOptions);
      if (response.status == 500) {
        consoleData(dataHashMap._action_code, response);
        return getResultFormat(
          "There is an issue detected at the server side."
        );
      } else if (response.status != 200) {
        consoleData(dataHashMap._action_code, response);
        return getResultFormat(
          "Could not connect to the server. Please try again later."
        );
      }
      try {
        const resultData = await response.json();
        consoleData(dataHashMap._action_code, resultData);
        if (resultData._access_key && resultData._access_key.length > 0) {
          setAccessKey(resultData._access_key);
        }
        return getResultFormat(resultData.msg, resultData.status, resultData);
      } catch (error) {
        return getResultFormat("Data parse error.");
      }
    } catch (error) {
      if (error == "TypeError: Failed to fetch") {
        error = "Could not connect to the server. Please try again";
      } else if (
        error == "TypeError: Network request failed" ||
        error == "[TypeError: Network request failed]"
      ) {
        error =
          "Could not connect to the server due to network issue. Please check your network connection";
      } else {
        error = "Could not reach server. (" + error + ")";
      }
      consoleData(dataHashMap._action_code, error);
      return getResultFormat(error);
    }
  }
}
function getResultFormat(errorMessage, status = "FAILED", data = null) {
  return {
    isSuccess: status == "SUCCESS",
    status: status,
    message: errorMessage,
    data: data,
  };
}
function consoleData(type = "", data = "") {
  console.log(type, data);
}
function getUrlPath(_action_code) {
  const BASE_URL = "http://localhost:8000";
  // const BASE_URL = "https://sotrue-backend-o7yx.onrender.com";
  return `${BASE_URL}/sotrueapp/playlist`;
}
export default ServerConnector;
