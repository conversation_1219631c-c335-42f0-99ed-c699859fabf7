export function _preventDoubleClick(lastClick, clickHappen = 3) {
    let now = new Date();
    let timeDiff = Math.abs(lastClick - now);
    let second = Math.floor((timeDiff / 1000))
    if (second >= clickHappen) {
        return true;
    }
    return false;
}
export function getImageDimensions(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
            const img = new Image();
            img.onload = () => {
                resolve({
                    width: img.width,
                    height: img.height,
                });
            };
            img.src = reader.result;
        };
        reader.readAsDataURL(file);
    });
}