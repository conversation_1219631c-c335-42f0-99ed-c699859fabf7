import { Form<PERSON><PERSON>rol<PERSON>abel, Switch } from '@mui/material'
import React from 'react';
import { styled } from '@mui/material/styles';


export const AppSwitch = ({
    checked = false,
    onChange,


}) => {
    return (
        <SotrueSwitch
            checked={checked}
            onChange={onChange}
            inputProps={{ 'aria-label': 'controlled' }} />
    )
}
const SotrueSwitch = styled((props) => (
    <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(({ theme }) => ({
    width: 32,
    height: 16,
    padding: 0,
    '& .MuiSwitch-switchBase': {
        padding: 0,
        transitionDuration: '300ms',
        '&.Mui-checked': {
            transform: 'translateX(16px)',
            color: '#fff',
            '& + .MuiSwitch-track': {
                backgroundColor: "#373737",
                opacity: 1,
                border: 0,
            },
            '&.Mui-disabled + .MuiSwitch-track': {
                opacity: 0.5,
            },
            '& .MuiSwitch-thumb': {
                color: '#FC6767',
            }
        },
        '&.Mui-focusVisible .MuiSwitch-thumb': {
            color: '#FC6767',
            border: '6px solid #FC6767',
        },
        '&.Mui-disabled .MuiSwitch-thumb': {
            color:
                theme.palette.mode === 'light'
                    ? theme.palette.grey[100]
                    : theme.palette.grey[600],
        },
        '&.Mui-disabled + .MuiSwitch-track': {
            opacity: theme.palette.mode === 'light' ? 0.7 : 0.3,
        },
    },
    '& .MuiSwitch-thumb': {
        boxSizing: 'border-box',
        width: 16,
        height: 16,
        transition: theme.transitions.create(['color'], {
            duration: 500,
        }),
    },
    '& .MuiSwitch-track': {
        borderRadius: 32 / 2,
        backgroundColor: '#39393D',
        opacity: 1,
        transition: theme.transitions.create(['background-color'], {
            duration: 500,
        }),
    },
}));