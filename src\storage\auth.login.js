import { XORCipher } from "./myCriptjs";

const WEB_ACCESS_KEY = "885df6bdad20b154c13f264d98975eabc80c0c21cd9d92bc3e51b1fd2c"

export function saveUserCredential(userDetails) {
    if (userDetails !== null) {
        // let key = XORCipher.encode(WEB_ACCESS_KEY, "playlistUserDetails");
        // let value = XORCipher.encode(WEB_ACCESS_KEY, JSON.stringify(userDetails));
        let key = "playlistUserDetails";
        let value = JSON.stringify(userDetails);
        localStorage.setItem(key, value);
    }

}
export function getUserCredential() {
    // let key = XORCipher.encode(WEB_ACCESS_KEY, "playlistUserDetails");
    let key = "playlistUserDetails";

    let userVal = localStorage.getItem(key);
    if (userVal === null) {
        return null;
    }
    // let userDetails = XORCipher.decode(WEB_ACCESS_KEY, userVal);
    let userDetails = userVal;
    return JSON.parse(userDetails);
}
export function getCurrentUserProfileSeq() {
    let userDetails = getUserCredential();
    if (userDetails !== null) {
        if (userDetails._profile_seq.length !== 0) {
            return userDetails._profile_seq;
        }
    }
    return -1;

}
export function getUserParticularDetails(detailsType) {
    let userDetails = getUserCredential();
    if (userDetails !== null) {
        if (userDetails.hasOwnProperty(detailsType)) {
            return userDetails[detailsType];
        }
    }
    return "";
}
export function changeUserCredential(changeValueArray) {
    let userDetails = getUserCredential();
    if (userDetails !== null) {
        changeValueArray.map((obj) => {
            userDetails[obj.key] = obj.value;
        });
        saveUserCredential(userDetails);
    }

}
export function removeUserCredential() {
    // let key = XORCipher.encode(WEB_ACCESS_KEY, "playlistUserDetails");
    let key = "playlistUserDetails";
    localStorage.removeItem(key)
}
export function setAccessKey(access_key) {
    localStorage.setItem("__playlist_access__key", access_key);
}
export function getAccessKey() {
    let accessKey = localStorage.getItem("__playlist_access__key");
    return accessKey;
}
export function removeAccessKey() {
    localStorage.removeItem("__playlist_access__key")
}
export function getAppDataLocal() {
    // let key = XORCipher.encode(WEB_ACCESS_KEY, "playListData");
    let key = "playListData";

    let appDataVal = localStorage.getItem(key);
    if (appDataVal === null) {
        return null;
    }
    // let appDataDetails = XORCipher.decode(WEB_ACCESS_KEY, appDataVal);
    let appDataDetails = appDataVal;
    return JSON.parse(appDataDetails);
}
export function saveAppDataLocal(appDataVal) {
    // let key = XORCipher.encode(WEB_ACCESS_KEY, "playListData");
    // let value = XORCipher.encode(WEB_ACCESS_KEY, JSON.stringify(appDataVal));
    let key = "playListData";
    let value = JSON.stringify(appDataVal);
    localStorage.setItem(key, value);
}
export function removeAppDataLocal() {
    // let key = XORCipher.encode(WEB_ACCESS_KEY, "playListData");
    let key = "playListData";
    localStorage.removeItem(key)
}
