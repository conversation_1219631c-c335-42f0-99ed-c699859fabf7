import { Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from "@mui/material";
import React from "react";
import { AppButton, AppOutlineButton } from "./AppButton";
import useAppTheme from "../../ui_theme/useAppTheme";

export default function AppConfirmation({
    open = false,
    onClose = null,
    onSubmit = null,
    title = "",
    message = "",
    positiveButton = "Yes",
    negativeButton = "No"
}) {
    const appTheme = useAppTheme();
    const handleCancel = () => {
        onClose();
    };
    const handleSubmit = () => {
        onClose();
        onSubmit();
    };

    return (

        <Dialog
            fullScreen={false}
            open={open}
            fullWidth={true}
            maxWidth={'xs'}
            onClose={onClose}
            PaperProps={{
                style: {
                    backgroundColor: appTheme.colors.background,
                },
            }}>
            <DialogTitle>
                {title}
            </DialogTitle>
            <DialogContent>
                <DialogContentText sx={{ color: appTheme.colors.textOnPrimary }}>
                    {message}
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <AppOutlineButton label={negativeButton} onClick={handleCancel} fullWidth={false} />
                <AppButton label={positiveButton} onClick={handleSubmit} fullWidth={false} />
            </DialogActions>
        </Dialog>
    );
}