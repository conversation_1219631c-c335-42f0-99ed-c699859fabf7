import { Autocomplete, Box, Checkbox, Chip, FormControl, FormHelperText, Grid, InputLabel, MenuItem, Select, TextField, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react'
import useAppTheme from '../../ui_theme/useAppTheme';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';

export const AppMultiSelectSearch = ({
    label = "",
    placeholder = "",
    selected = [],
    defaultValue = "",
    options = [],
    style = null,
    errorMessage = "",
    disabled = false,
    marginBottom = "1.5rem",
    disableColor = null,
    onSelectChange = null,
    menuItemButton = null,
    limitTags = 2,
    ...props
}) => {
    const appTheme = useAppTheme();
    const [selectedValue, setSelectedValue] = useState([]);
    useEffect(() => {
        let defaultV = [];

        options.map(obj => {
            if (selected.includes(String(obj.value))) {
                defaultV.push(obj);
            }
        })
        // console.log("defaultV", defaultV)
        setSelectedValue(defaultV);
    }, [options]);
    const onChange = (event, newValue) => {
        if (onSelectChange) {
            setSelectedValue(newValue);
            let tempList = [];
            newValue.map((item) => {
                tempList.push(item.value);
            });
            onSelectChange(tempList);
        }
    }
    return (
        <Box sx={{
            marginBottom: marginBottom
        }}>
            {
                label.length != 0 ?
                    <Typography sx={{
                        color: appTheme.colors.inputLabelColor,
                        fontSize: appTheme.dimensions.inputLabelSize,
                        marginBottom: "0.3rem",
                        marginLeft: "0.2rem"
                    }}>{label}</Typography>
                    : null
            }
            <Grid container spacing={0}>
                <Grid item xs >
                    <FormControl
                        variant="outlined" fullWidth={true} size="small" margin="dense"
                        error={errorMessage.length !== 0}
                        sx={{
                            '& legend': { display: 'none' },
                            marginTop: 0,

                        }}
                    >
                        <Autocomplete
                            multiple
                            id="tags-outlined"
                            options={options}
                            getOptionLabel={(option) => option.label}
                            defaultValue={[]}
                            value={selectedValue}
                            limitTags={limitTags}
                            disableCloseOnSelect
                            onChange={onChange}
                            renderOption={(props, option, { selected }) => {
                                const { key, id, ...optionProps } = props;
                                return (
                                    <MenuItem key={`${key}_${id}`} value={option.value} {...optionProps}>
                                        <div className='d-flex w-100'>
                                            {option.label}
                                            {
                                                selected ?
                                                    <div className='ms-auto ps-4'>
                                                        <CheckRoundedIcon sx={{ color: appTheme.colors.primary }} />
                                                    </div>
                                                    : null
                                            }

                                        </div>

                                    </MenuItem>
                                )
                            }}
                            renderTags={(value, getTagProps) =>
                                value.map((option, index) => {
                                    const { key, ...tagProps } = getTagProps({ index });
                                    return (
                                        <Chip size="small" sx={{
                                            backgroundColor: "#CCC",
                                            color: appTheme.colors.inputColor,
                                        }} variant="outlined" label={option.label} key={`${key}_${index}`} {...tagProps} />
                                    );
                                })
                            }
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    variant="outlined"
                                    fullWidth
                                    size="small"
                                    placeholder={placeholder}
                                    sx={{
                                        '& legend': { display: 'none' },
                                        borderRadius: appTheme.dimensions.inputRadius + "px",
                                        background: disableColor ? disableColor : appTheme.colors.inputBackgroundColor,
                                        input: {
                                            background: disableColor ? disableColor : appTheme.colors.inputBackgroundColor,
                                            color: appTheme.colors.inputColor,

                                            marginTop: 0,
                                            '&::placeholder': {
                                                color: appTheme.colors.inputPlaceholderColor,
                                                fontSize: appTheme.dimensions.defaultFontSize
                                            },
                                            "&:disabled": {
                                                // color: disableColor,
                                                // "-webkit-text-fill-color": disableColor
                                            },
                                            borderRadius: appTheme.dimensions.inputRadius + "px",


                                        },
                                        "& .MuiOutlinedInput-root": {
                                            borderRadius: appTheme.dimensions.inputRadius + "px",
                                        },
                                        "& .MuiOutlinedInput-notchedOutline": {
                                            top: "0px",
                                            borderRadius: appTheme.dimensions.inputRadius + "px",
                                        },
                                        "& .Mui-focused .MuiOutlinedInput-notchedOutline": {
                                            top: "0px",
                                            borderRadius: appTheme.dimensions.inputRadius + "px",
                                        },
                                    }}
                                />
                            )}
                            ListboxProps={{
                                sx: {
                                    color: appTheme.colors.inputColor,
                                }
                            }}>

                        </Autocomplete>
                        <FormHelperText>{errorMessage}</FormHelperText>
                    </FormControl>

                </Grid>

            </Grid>

        </Box>
    );
}
export const DropdownButtonItem = ({
    buttonLabel = "",
    buttonPress = null
}) => {
    return (
        <MenuItem className='dropdownButton' onClick={() => buttonPress()}>
            <div className='dropdownButton__text'>
                {buttonLabel}
            </div>
        </MenuItem>
    )
}