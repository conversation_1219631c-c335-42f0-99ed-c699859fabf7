import { Grid } from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  AppInput,
  AppTextareaInput,
} from "../../components/ui_component/AppInput";
import { AppSelect } from "../../components/ui_component/AppSelect";
import {
  AppButton,
  AppOutlineButton,
} from "../../components/ui_component/AppButton";
import { checkValueLength } from "../../util/StringUtils";
import { closeLoader, showLoader } from "../../state/slice/loaderSlice";
import {
  addUpdateSeasonsApi,
  getSeasonDetailsApi,
  getShowListApi,
  getYearOfReleaseListApi,
  setSeasonStatusApi,
} from "../../api/Apis";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { openSnackBar } from "../../state/slice/snackbarSlice";
import AppConfirmation from "../../components/ui_component/AppConfirmation";
import BackButton from "../../components/common/BackButton";
import AppMediaSelection from "../../components/ui_component/AppMediaSelection";
import AppConstants from "../../data/AppConstants";
const AddSeasonScreen = ({}) => {
  const { state } = useLocation();
  const [inputValues, setInputValues] = useState({
    title: "",
    description: "",
    showSeq: state.showSeq,
    yearOfRelease: "",
    gridTitle: "",
    titleErr: "",
    descriptionErr: "",
    showSeqErr: "",
    yearOfReleaseErr: "",
    gridTitleErr: "",
  });
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [showList, setShowList] = useState([]);
  const [yearOfReleaseList, setYearOfReleaseList] = useState([]);

  const [selectedThumbnailImage, setSelectedThumbnailImage] = useState(null);
  const [selectedThumbnailImageFile, setSelectedThumbnailImageFile] =
    useState(null);
  const [selectedThumbnailImageFileName, setSelectedThumbnailImageFileName] =
    useState("");
  const [selectedThumbnailImageErr, setSelectedThumbnailImageErr] =
    useState("");

  const [selectedBannerFileImage, setSelectedBannerFileImage] = useState(null);
  const [selectedBannerImageFile, setSelectedBannerImageFile] = useState(null);
  const [selectedBannerImageFileName, setSelectedBannerImageFileName] =
    useState("");
  const [selectedBannerImageFileErr, setSelectedBannerImageFileErr] =
    useState("");

  const [dbFileData, setDbFileData] = useState({});

  const [confirmation, setConfirmation] = React.useState({
    open: false,
    title: "",
    message: "",
    onSubmit: null,
  });

  useEffect(() => {
    callShowDetailsService();
    getShowListService();
    getYearOfReleaseListService();
  }, []);
  const getShowListService = () => {
    dispatch(showLoader());
    getShowListApi().then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        let tempList = [];
        result.data.data.map((obj) => {
          tempList.push({ label: obj.title, value: obj.show_seq });
        });
        setShowList(tempList);
      } else {
        setShowList([]);
      }
    });
  };
  const callShowDetailsService = () => {
    if (checkValueLength(state.seasonSeq) && state.seasonSeq != -1) {
      dispatch(showLoader());
      getSeasonDetailsApi(state.seasonSeq).then((result) => {
        dispatch(closeLoader());
        if (result.isSuccess) {
          const dbData = result.data.data[0];
          let data = {
            title: dbData.title,
            gridTitle: checkValueLength(dbData.grid_title)
              ? dbData.grid_title
              : "",
            description: dbData.description,
            showSeq: dbData.show_seq,
            yearOfRelease: checkValueLength(dbData.release_year)
              ? dbData.release_year
              : "",
          };
          let dataFile = {
            thumb_file: dbData.cover_image,
            thumb_file_name: "show thumb",
            banner_file: dbData.banner_image,
            banner_file_name: "show banner image",
          };
          setSelectedThumbnailImageFile(dataFile.thumb_file);
          setSelectedThumbnailImageFileName(dataFile.thumb_file_name);

          setSelectedBannerImageFile(dataFile.banner_file);
          setSelectedBannerImageFileName(dataFile.banner_file_name);

          setDbFileData(dataFile);
          setInputValues((prevState) => ({
            ...prevState,
            ...data,
          }));
        } else {
          dispatch(
            openSnackBar({
              open: true,
              message: result.message,
              type: "FAILED",
            })
          );
          // navigate(-1)
        }
      });
    }
  };

  const handleInputChange = (name) => (value) => {
    setInputValues((prevState) => ({
      ...prevState,
      [name]: value,
      [name + "Err"]: "",
    }));
  };
  const formValid = () => {
    let isValid = true;
    let errorData = {
      titleErr: "",
      descriptionErr: "",
      showSeqErr: "",
      yearOfReleaseErr: "",
      gridTitleErr: "",
    };
    if (state.seasonSeq == -1) {
      if (selectedThumbnailImage == null) {
        setSelectedThumbnailImageErr("Please add the Thumbnail Image");
        isValid = false;
      }
      if (selectedBannerFileImage == null) {
        setSelectedBannerImageFileErr("Please add the banner Image");
        isValid = false;
      }
      if (!checkValueLength(inputValues.showSeq)) {
        errorData.showSeqErr = "Please select Show";
      }
    }
    if (!checkValueLength(inputValues.title)) {
      errorData.titleErr = "Title is required";
      isValid = false;
    }
    if (!checkValueLength(inputValues.gridTitle)) {
      errorData.gridTitleErr = "Grid Title is required";
      isValid = false;
    }
    if (!checkValueLength(inputValues.description)) {
      errorData.descriptionErr = "Description is required";
      isValid = false;
    }
    setInputValues((prevState) => ({
      ...prevState,
      ...errorData,
    }));
    return isValid;
  };
  const saveBtnPress = () => {
    if (formValid()) {
      dispatch(showLoader());
      submitSeasonData();
    }
  };
  const submitSeasonData = () => {
    let seasonSeq = state.seasonSeq;
    let imageHasMap = [];
    if (selectedThumbnailImage != null) {
      imageHasMap.push({
        inputName: "cover_file",
        imageData: selectedThumbnailImage,
      });
    }
    if (selectedBannerFileImage != null) {
      imageHasMap.push({
        inputName: "banner_file",
        imageData: selectedBannerFileImage,
      });
    }
    let titleVal = encodeURIComponent(inputValues.title);
    let gridTitle = encodeURIComponent(inputValues.gridTitle);
    let descriptionVal = encodeURIComponent(inputValues.description);
    let yearOfRelease = "";
    if (checkValueLength(inputValues.yearOfRelease)) {
      yearOfRelease = inputValues.yearOfRelease;
    }
    addUpdateSeasonsApi(
      seasonSeq,
      inputValues.showSeq,
      titleVal,
      gridTitle,
      descriptionVal,
      yearOfRelease,
      imageHasMap
    ).then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "SUCCESS",
          })
        );
        if (seasonSeq == -1) {
          navigate(-1);
        }
      } else {
        let errorMsgShow = false;
        if (result.data && result.data.err_cd == "E006" && result.data.data) {
          let errorMsgData = {
            titleErr: "",
            descriptionErr: "",
            showSeqErr: "",
            yearOfReleaseErr: "",
            gridTitleErr: "",
          };
          if (result.data.data.title) {
            errorMsgData.titleErr = result.data.data.title;
            errorMsgShow = true;
          }
          if (result.data.data.description) {
            errorMsgData.descriptionErr = result.data.data.description;
            errorMsgShow = true;
          }
          if (result.data.data.show_seq) {
            errorMsgData.showSeqErr = result.data.data.show_seq;
            errorMsgShow = true;
          }
          if (result.data.data.release_year) {
            errorMsgData.yearOfReleaseErr = result.data.data.release_year;
            errorMsgShow = true;
          }
          if (result.data.data.grid_title) {
            errorMsgData.gridTitleErr = result.data.data.grid_title;
            errorMsgShow = true;
          }
          setInputValues((prevState) => ({
            ...prevState,
            ...errorMsgData,
          }));
        }
        if (!errorMsgShow) {
          dispatch(
            openSnackBar({
              open: true,
              message: result.message,
              type: "FAILED",
            })
          );
        }
      }
    });
  };
  const handleConfirmationClose = () => {
    setConfirmation((prevState) => ({
      ...prevState,
      open: false,
    }));
  };
  const deleteBtnPress = () => {
    setConfirmation({
      open: true,
      title: "Confirmation",
      message: "Do you want to delete this season?",
      onSubmit: () => {
        callSeasonDeleteService(state.seasonSeq);
      },
    });
  };
  const callSeasonDeleteService = (seasonSeq) => {
    dispatch(showLoader());
    setSeasonStatusApi(seasonSeq, "INACTIVE").then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "SUCCESS",
          })
        );
        navigate(-1);
      } else {
        dispatch(
          openSnackBar({
            open: true,
            message: result.message,
            type: "FAILED",
          })
        );
      }
    });
  };
  const thumbnailImageClick = (clickID, obj) => {
    setSelectedThumbnailImageErr("");
    if (clickID == "SELECT_MEDIA") {
      if (obj.mediaData.length != 0) {
        let mediaData = obj.mediaData[0];
        if (validateThumbnailImage(mediaData.type)) {
          if (mediaData.size <= AppConstants.DEFAULT_UPLOAD_IMAGE_SIZE) {
            setSelectedThumbnailImage(mediaData);
            setSelectedThumbnailImageFile(URL.createObjectURL(mediaData));
            setSelectedThumbnailImageFileName(mediaData.name);
          } else {
            setSelectedThumbnailImageErr("Image size should be less than 5MB");
          }
        } else {
          setSelectedThumbnailImageErr("file format should be jpeg/png/jpg");
        }
      }
    } else if (clickID == "DELETE_MEDIA") {
      setSelectedThumbnailImage(null);
      if (dbFileData.hasOwnProperty("thumb_file")) {
        setSelectedThumbnailImageFile(dbFileData.thumb_file);
        setSelectedThumbnailImageFileName(dbFileData.thumb_file_name);
      } else {
        setSelectedThumbnailImageFile(null);
        setSelectedThumbnailImageFileName("");
      }
    }
  };
  const bannerImageClick = (clickID, obj) => {
    setSelectedBannerImageFileErr("");
    if (clickID == "SELECT_MEDIA") {
      if (obj.mediaData.length != 0) {
        let mediaData = obj.mediaData[0];
        if (validateThumbnailImage(mediaData.type)) {
          if (mediaData.size <= AppConstants.DEFAULT_UPLOAD_IMAGE_SIZE) {
            setSelectedBannerFileImage(mediaData);
            setSelectedBannerImageFile(URL.createObjectURL(mediaData));
            setSelectedBannerImageFileName(mediaData.name);
          } else {
            setSelectedBannerImageFileErr("Image size should be less than 5MB");
          }
        } else {
          setSelectedBannerImageFileErr("file format should be jpeg/png/jpg");
        }
      }
    } else if (clickID == "DELETE_MEDIA") {
      setSelectedBannerFileImage(null);
      if (dbFileData.hasOwnProperty("banner_file")) {
        setSelectedBannerImageFile(dbFileData.banner_file);
        setSelectedBannerImageFileName(dbFileData.banner_file_name);
      } else {
        setSelectedBannerImageFile(null);
        setSelectedBannerImageFileName("");
      }
    }
  };
  const validateThumbnailImage = (mediaType) => {
    let validMedia = false;
    if (
      mediaType === "image/png" ||
      mediaType === "image/jpg" ||
      mediaType === "image/jpeg"
    ) {
      validMedia = true;
    }
    return validMedia;
  };
  const getYearOfReleaseListService = () => {
    dispatch(showLoader());
    getYearOfReleaseListApi().then((result) => {
      dispatch(closeLoader());
      if (result.isSuccess) {
        let tempList = [];
        result.data.data.map((item) => {
          tempList.push({ label: item, value: item });
        });
        setYearOfReleaseList(tempList);
      } else {
        setYearOfReleaseList([]);
      }
    });
  };
  return (
    <>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <BackButton
            heading={
              state.seasonSeq == -1 ? "Upload New Season" : "Update Season"
            }
          />
        </Grid>
      </Grid>
      <Grid container spacing={2}>
        <Grid item xs={12} md={8}>
          <Grid container spacing={2}>
            {state.seasonSeq && state.seasonSeq == -1 && (
              <Grid item xs={12} md={6}>
                <AppSelect
                  label="Show"
                  placeholder="Please Select"
                  options={showList}
                  value={inputValues.showSeq}
                  onSelectChange={handleInputChange("showSeq")}
                  errorMessage={inputValues.showSeqErr}
                  marginBottom="16px"
                />
              </Grid>
            )}
            <Grid item xs={12} md={6}>
              <AppInput
                label="Title"
                placeholder="Type here"
                value={inputValues.title}
                onTextChange={handleInputChange("title")}
                maxLength={50}
                marginBottom="18px"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <AppInput
                label="Grid Title"
                placeholder="Type here"
                value={inputValues.gridTitle}
                onTextChange={handleInputChange("gridTitle")}
                maxLength={24}
                marginBottom="18px"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <AppTextareaInput
                multiline
                minRows={3}
                maxRows={3}
                label="Description"
                placeholder="Type here"
                maxLength={340}
                value={inputValues.description}
                onTextChange={handleInputChange("description")}
                marginBottom="18px"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <AppSelect
                label="Year of Release"
                placeholder="Please Select"
                options={yearOfReleaseList}
                errorMessage={inputValues.yearOfReleaseErr}
                value={inputValues.yearOfRelease}
                onSelectChange={handleInputChange("yearOfRelease")}
                marginBottom="18px"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <AppMediaSelection
                headings="Upload Thumbnail Image"
                secondHeading="JPG file, 1080 x 1920 px"
                selectedMedia={selectedThumbnailImage}
                onMediaChange={thumbnailImageClick}
                displayMediaFile={selectedThumbnailImageFile}
                mediaFileName={selectedThumbnailImageFileName}
                mediaErr={selectedThumbnailImageErr}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <AppMediaSelection
                headings="Upload Banner image"
                secondHeading="JPG file, 1920 x 1080 px"
                selectedMedia={selectedBannerFileImage}
                onMediaChange={bannerImageClick}
                displayMediaFile={selectedBannerImageFile}
                mediaFileName={selectedBannerImageFileName}
                mediaErr={selectedBannerImageFileErr}
              />
            </Grid>
            <Grid item xs={12}>
              <Grid container spacing={2}>
                <Grid item xs={12}></Grid>
                {state.seasonSeq !== -1 ? (
                  <Grid item xs={12} md={4} mb={3}>
                    <AppOutlineButton
                      label="Delete"
                      onClick={() => deleteBtnPress()}
                    />
                  </Grid>
                ) : null}

                <Grid item xs={12} md={6} mb={3}>
                  <AppButton
                    label={`${state.seasonSeq == -1 ? "Save" : "Update"}`}
                    onClick={() => saveBtnPress()}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={4}>
          <div className="m-5" />
          {/* <UploadGuideComponent />
                    <div className='mt-5'>
                        <LastSubmittedComponent data={lastSubmittedData} />
                    </div> */}
        </Grid>
      </Grid>
      <AppConfirmation
        open={confirmation.open}
        onClose={handleConfirmationClose}
        onSubmit={confirmation.onSubmit}
        title={confirmation.title}
        message={confirmation.message}
      />
    </>
  );
};

export default AddSeasonScreen;
