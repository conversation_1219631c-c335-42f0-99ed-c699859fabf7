
import { Button, Dialog, DialogActions, DialogContent, Grid } from '@mui/material';
import React, { Component, useEffect, useState } from 'react'


const ViewImagePopup = ({
    displayPopup = false,
    imageUrl = null,
}) => {
    const [openPopup, setOpenPopup] = useState(false);
    const [imgUrlValue, setImgUrlValue] = useState(imageUrl);
    useEffect(() => {
        setOpenPopup(displayPopup)
    }, [displayPopup])


    const handleModalShowHide = () => {
        setOpenPopup(false);
    }

    return (
        <Dialog
            fullWidth={true}
            maxWidth="md"
            open={openPopup}
            onClose={() => handleModalShowHide()}>
            <DialogContent>
                <Grid container>
                    <Grid item xs={12}>
                        <div className="text-center" style={{ position: "relative", minHeight: "50vh" }}>
                            <img src={imgUrlValue} className="img-fluid viewPopupImage" alt="View Image" />
                        </div>
                    </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                <Button autoFocus onClick={() => handleModalShowHide()} color="primary">
                    Close
                </Button>
            </DialogActions>
        </Dialog>
    )
}

export default ViewImagePopup;