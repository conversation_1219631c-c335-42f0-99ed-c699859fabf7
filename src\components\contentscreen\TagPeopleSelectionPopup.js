import { Dialog, DialogActions, DialogContent, DialogTitle, Grid } from '@mui/material'
import React, { useEffect, useState } from 'react'
import useAppTheme from '../../ui_theme/useAppTheme';
import { AppButton, AppOutlineButton } from '../ui_component/AppButton';
import { AppInput } from '../ui_component/AppInput';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
import { closeLoader, showLoader } from '../../state/slice/loaderSlice';
import { getUserTagListApi } from '../../api/Apis';
import { useDispatch } from 'react-redux';
import { getCurrentUserProfileSeq } from '../../storage/auth.login';
import Placeholder from '../../assets/images/user_image_place_holder.png'

const TagPeopleSelectionPopup = ({
    open = false,
    onClose = null,
    onTagPeople = null,
    selectedTagPeople = []
}) => {
    const appTheme = useAppTheme();
    const dispatch = useDispatch();
    const [searchQuery, setSearchQuery] = useState("");
    const [tagPeopleList, setTagPeopleList] = useState([]);
    const [pageErrorMsg, setPageErrorMsg] = useState("");
    const [searchHappen, setSearchHappen] = useState(false)
    const [filterItemList, setFilterItemList] = useState([]);
    useEffect(() => {
        getPeopleTagListService();
    }, [])


    const getPeopleTagListService = () => {
        const profileSeq = getCurrentUserProfileSeq();
        dispatch(showLoader());
        getUserTagListApi(profileSeq).then(result => {
            dispatch(closeLoader());
            if (result.isSuccess) {
                setPageErrorMsg("");
                let tempList = [];
                let itemList = [];
                selectedTagPeople.map(obj => {
                    itemList.push(String(obj.profile_seq))
                })
                result.data.data.map(obj => {
                    let isChecked = false;

                    if (itemList.includes(String(obj.profile_seq))) {
                        isChecked = true;
                    }
                    tempList.push({
                        profile_seq: obj.profile_seq,
                        user_seq: obj.user_seq,
                        display_name: obj.display_name,
                        user_handle: obj.user_handle,
                        profile_image: obj.profile_picture,
                        isChecked: isChecked
                    })
                })
                setTagPeopleList(tempList);
                setTagPeopleList(tempList);
                filterTagPeopleList("", tempList);
            }
            else {
                setTagPeopleList([]);
                filterTagPeopleList("", []);
                setPageErrorMsg(result.message);
            }
        });
    }
    const filterTagPeopleList = (queryValue, list = []) => {
        let tempList = JSON.parse(JSON.stringify(list));
        if (queryValue.length > 0) {
            tempList = tempList.filter((item) => item.display_name.toLowerCase().includes(queryValue.toLowerCase()) || item.user_handle.toLowerCase().includes(queryValue.toLowerCase()));
        }
        setFilterItemList([...[], ...tempList]);

    }
    useEffect(() => {
        const delay = setTimeout(() => {
            if (searchHappen) {
                filterTagPeopleList(searchQuery, tagPeopleList);
            }
        }, 300)
        return () => clearTimeout(delay)
    }, [searchQuery]);

    const handleCancel = () => {
        onClose();
    };
    const submitBtnPress = () => {
        let tempList = JSON.parse(JSON.stringify(tagPeopleList));
        tempList = tempList.filter((item) => item.isChecked);
        onTagPeople("SUBMIT", { data: tempList });
    }
    const onInputSearch = (value) => {
        setSearchHappen(true);
        setSearchQuery(value);
    }
    const TagPeopleItem = ({ data, itemRowPress }) => {
        return (
            <div className='tagPeopleItem d-flex align-items-center'
                onClick={() => itemRowPress(data.profile_seq)}>
                <div className='me-2'>
                    <img src={data.profile_image != null ? data.profile_image : Placeholder} className='tagPeopleItem__profileImage' />
                </div>
                <div className='tagPeopleItem__name'>{data.display_name}</div>
                {
                    data.isChecked ?
                        <div className='ms-auto ps-4' style={{ color: appTheme.colors.primary }}>
                            <CheckRoundedIcon fontSize='small' />
                        </div>
                        : null
                }

            </div>
        )
    }
    const itemRowPress = (id) => {
        let tempList = JSON.parse(JSON.stringify(tagPeopleList));
        tempList.forEach((item) => {
            if (String(item.profile_seq) === String(id)) {
                item.isChecked = !item.isChecked;
            }
        });
        setTagPeopleList([...tempList]);
        filterTagPeopleList(searchQuery, tempList)

    }
    return (
        <Dialog
            fullScreen={false}
            open={open}
            fullWidth={true}
            maxWidth={'xs'}
            onClose={handleCancel}
            PaperProps={{
                style: {
                    backgroundColor: appTheme.colors.background,
                },
            }}>
            <DialogTitle>
                Select Tag Peoples
            </DialogTitle>
            <DialogContent>
                <Grid container>
                    <Grid item xs={12}>
                        <AppInput
                            label={""}
                            placeholder={"Search People..."}
                            value={searchQuery}
                            onTextChange={(e) => onInputSearch(e)}
                            marginBottom='22px' />

                    </Grid>
                    <Grid item xs={12} >
                        <div className='tagPeopleBox'>
                            {
                                filterItemList.map((obj, i) => {
                                    return <TagPeopleItem key={i} data={obj} itemRowPress={itemRowPress} />
                                })
                            }
                        </div>

                    </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                <AppOutlineButton label={"Cancel"} onClick={handleCancel} fullWidth={false} />
                <AppButton label={"Submit"} onClick={() => submitBtnPress()} fullWidth={false} />
            </DialogActions>
        </Dialog>
    )
}

export default TagPeopleSelectionPopup