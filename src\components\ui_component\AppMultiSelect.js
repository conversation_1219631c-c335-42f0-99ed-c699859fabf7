import { Box, FormControl, FormHelperText, Grid, InputLabel, MenuItem, Select, Typography } from '@mui/material';
import React from 'react'
import useAppTheme from '../../ui_theme/useAppTheme';
import KeyboardArrowDownRoundedIcon from '@mui/icons-material/KeyboardArrowDownRounded';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
export const AppMultiSelect = ({
    label = "",
    placeholder = "",
    value = [],
    defaultValue = "",
    options = [],
    style = null,
    errorMessage = "",
    disabled = false,
    marginBottom = "1.5rem",
    disableColor = null,
    onSelectChange = null,
    menuItemButton = null,
    ...props
}) => {
    const appTheme = useAppTheme();
    const onChange = (event) => {
        if (onSelectChange) onSelectChange(event.target.value);
    }
    return (
        <Box sx={{
            marginBottom: marginBottom
        }}>
            {
                label.length != 0 ?
                    <Typography sx={{
                        color: appTheme.colors.inputLabelColor,
                        fontSize: appTheme.dimensions.inputLabelSize,
                        marginBottom: "0.3rem",
                        marginLeft: "0.2rem"
                    }}>{label}</Typography>
                    : null
            }
            <Grid container spacing={0}>
                <Grid item xs >
                    <FormControl
                        variant="outlined" fullWidth={true} size="small" margin="dense"
                        error={errorMessage.length !== 0}
                        sx={{
                            '& legend': { display: 'none' },
                            marginTop: 0,

                        }}
                    >
                        <Select
                            defaultValue=""
                            label={label}
                            multiple
                            variant="outlined"
                            margin="dense"
                            onChange={onChange}
                            value={value}
                            placeholder={placeholder}
                            IconComponent={KeyboardArrowDownRoundedIcon}
                            displayEmpty
                            {...props}
                            renderValue={
                                (selected) => {
                                    if (selected.length === 0) {
                                        return <div
                                            style={{
                                                color: appTheme.colors.inputPlaceholderColor,
                                                fontSize: appTheme.dimensions.defaultFontSize,
                                                opacity: 0.5
                                            }}>{placeholder}</div>
                                    }
                                    else {
                                        const optionsData = options.filter(option => selected.includes(option.value));
                                        return optionsData.map(option => option.label).join(", ")
                                    }
                                }
                            }
                            MenuProps={{
                                PaperProps: {
                                    style: {
                                        maxHeight: 48 * 4.5 + 8,
                                    },
                                },

                                sx: {
                                    '& .MuiMenu-paper': {
                                        color: appTheme.colors.inputColor,
                                        paddingRight: "1px"

                                    },
                                    '& .MuiMenuItem-root:hover': {
                                        backgroundColor: '#CCC',
                                        color: "#FC6767"
                                    },
                                    '& .Mui-selected': {
                                        backgroundColor: "#CCC",
                                        color: "#FC6767"
                                    }
                                }
                            }}
                            sx={{
                                '& legend': { display: 'none' },
                                background: appTheme.colors.inputBackgroundColor,
                                color: appTheme.colors.inputColor,
                                paddingRight: "28px",
                                marginTop: 0,
                                '&::placeholder': {
                                    color: appTheme.colors.inputPlaceholderColor,
                                    fontSize: appTheme.dimensions.defaultFontSize
                                },
                                "&:disabled": {
                                    color: disableColor,
                                    "-webkit-text-fill-color": disableColor
                                },
                                borderRadius: appTheme.dimensions.inputRadius + "px",
                                "& .MuiOutlinedInput-root": {
                                    borderRadius: appTheme.dimensions.inputRadius + "px",
                                },
                                "& .MuiOutlinedInput-notchedOutline": {
                                    top: "0px",
                                    borderRadius: appTheme.dimensions.inputRadius + "px",
                                },
                                "& .Mui-focused .MuiSelect-iconOutlined-notchedOutline": {
                                    top: "0px",
                                    borderRadius: appTheme.dimensions.inputRadius + "px",
                                },
                            }}>
                            {
                                options ? options.map((obj, i) => {
                                    return <MenuItem key={i} value={obj.value}>
                                        <div className='d-flex w-100 align-items-center'>
                                            {
                                                obj.icon && obj.icon
                                            }
                                            {obj.label}
                                            {
                                                value.includes(obj.value) ?
                                                    <div className='ms-auto ps-4'>
                                                        <CheckRoundedIcon />
                                                    </div>
                                                    : null
                                            }

                                        </div>

                                    </MenuItem>
                                })
                                    : null

                            }
                            {menuItemButton}
                        </Select>

                        <FormHelperText>{errorMessage}</FormHelperText>
                    </FormControl>

                </Grid>

            </Grid>

        </Box>
    );
}
export const DropdownButtonItem = ({
    buttonLabel = "",
    buttonPress = null
}) => {
    return (
        <MenuItem className='dropdownButton' onClick={() => buttonPress()}>
            <div className='dropdownButton__text'>
                {buttonLabel}
            </div>
        </MenuItem>
    )
}