import { Paper } from '@mui/material'
import React, { useState } from 'react';
import ImagePlaceholder from '../../assets/images/default_image.jpg'
import PlayArrowRoundedIcon from '@mui/icons-material/PlayArrowRounded';
const UploadGuideComponent = () => {
    const [guideList, setguideList] = useState([
        { id: 1, title: "How to Upload Your Playlist Correctly", },
        { id: 2, title: "The Complete Way to Organize Your Playlist Content", },
        { id: 3, title: "How to Get More Impressions on Your Playlist", },
    ])

    const GuideItem = ({
        item
    }) => {
        return (
            <div className='d-flex mb-2 align-items-center '>
                <div className='guideMediaBox shadow'>
                    <img src={ImagePlaceholder} alt="Guide Image" className='guideMediaBox__image' />
                    <div className='playBoxBackdrop shadow' />
                    <div className='playBox'>

                        <PlayArrowRoundedIcon />

                    </div>
                </div>
                <div className='guideTextBox'>
                    <div className='guideTextBox__title'>{item.title}</div>
                </div>
            </div>
        );
    }
    return (
        <Paper elevation={3} className='overviewCard d-flex flex-column flex-grow-1 cardGapBottom '>
            <div className='d-flex CardHeader'>
                <div className='paperHeader__title'>Guides</div>
            </div>
            <div className='mt-2 '>
                {guideList.map((guide, i) => {
                    return <GuideItem key={i} item={guide} />
                })}
            </div>
        </Paper>
    )
}

export default UploadGuideComponent