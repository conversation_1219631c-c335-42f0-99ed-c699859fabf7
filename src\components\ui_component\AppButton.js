import { Button } from '@mui/material';
import React from 'react'
import useAppTheme from '../../ui_theme/useAppTheme';


export const AppButton = ({ label = "", onClick, style = {},
    fullWidth = true, upperCase = false, startIcon = null, className = "", ...props }) => {
    const appTheme = useAppTheme();
    return (
        <Button
            className={className}
            variant='contained'
            sx={{
                textTransform: upperCase ? 'uppercase' : 'none',
                borderRadius: appTheme.dimensions.inputRadius + "px",
            }}
            fullWidth={fullWidth}
            onClick={onClick} style={style} size="medium"
            startIcon={startIcon} {...props}>{label}</Button>
    );

}
export const AppOutlineButton = ({ label = "", onClick, style = {},
    fullWidth = true, upperCase = false, startIcon = null, ...props }) => {
    const appTheme = useAppTheme();
    return (
        <Button
            variant='outlined'
            sx={{
                textTransform: upperCase ? 'uppercase' : 'none',
                borderRadius: appTheme.dimensions.inputRadius + "px",

            }}
            fullWidth={fullWidth}
            onClick={onClick} style={style} size="medium"
            startIcon={startIcon} {...props}>{label}</Button>
    );

}