import { FormHelperText, Grid } from '@mui/material'
import React, { useEffect, useRef, useState } from 'react'
import DeleteForeverRoundedIcon from '@mui/icons-material/DeleteForeverRounded';
import AppConstants from '../../data/AppConstants';
import { useDispatch } from 'react-redux';
import { closeLoader, showLoader } from '../../state/slice/loaderSlice';
import { openSnackBar } from '../../state/slice/snackbarSlice';
import { getCurrentUserProfileSeq } from '../../storage/auth.login';
import { AddEpisodeClipApi, setEpisodeClipStatusApi, updateEpisodeClipApi } from '../../api/Apis';
import { checkValueLength } from '../../util/StringUtils';
import AppConfirmation from '../ui_component/AppConfirmation';
import DELETE_ICON from '../../assets/images/svgs/Delete.svg';
import EDIT_ICON from '../../assets/images/svgs/Edit.svg';
import SELECT_MEDIA_ICON from '../../assets/images/svgs/Upload_a_video.svg';
const ClipItemRow = ({
    index = 0,
    data = {},
    clipRowBtnPress = null,
    episodeSeq = ""

}) => {
    const mediaRefPress = useRef(null);
    const dispatch = useDispatch();
    const [clipItemSeq, setClipItemSeq] = useState(data.clipSeq);
    const [clipTitle, setClipTitle] = useState(data.clipTitle);
    const [clipTitleErr, setClipTitleErr] = useState("");

    const [clipFile, setClipFile] = useState(data.clipFile);
    const [clipDisplayFile, setClipDisplayFile] = useState(data.clipDisplayFile);
    const [clipFileName, setClipFileName] = useState(data.clipDisplayFile);

    const [mediaErrorMessage, setMediaErrorMessage] = useState("");


    const [isUploading, setIsUploading] = useState(false);
    const [inputEditable, setInputEditable] = useState(true);
    const [confirmation, setConfirmation] = React.useState({
        open: false,
        title: "",
        message: "",
        onSubmit: null
    });

    useEffect(() => {
        if (data.clipSeq != -1) {
            setInputEditable(false)
        }
    }, []);

    const handleClipTitle = (e) => {
        setClipTitle(e.target.value);
    }
    const uploadClipBtnPress = () => {
        if (formValid()) {
            dispatch(showLoader());
            submitEpisodeClipData()
        }
    }
    const handleCrossClick = () => {
        clipRowBtnPress("DELETE_ROW", { index: index, clipSeq: clipItemSeq })
    }

    const selectClipFile = () => {
        mediaRefPress.current.click();
    }
    const onInputChange = (e) => {
        setMediaErrorMessage("")
        if (e.target.files.length != 0) {
            const mediaData = e.target.files[0];
            const fileMaxSize = AppConstants.DEFAULT_UPLOAD_VIDEO_SIZE * 1024 * 1000;
            if (validateThumbnailVideo(mediaData.type)) {
                if (mediaData.size <= fileMaxSize) {
                    // console.log("mediaData.name", mediaData.name)
                    setClipFile(mediaData);
                    setClipDisplayFile(URL.createObjectURL(mediaData));
                    setClipFileName(mediaData.name);
                }
                else {
                    setMediaErrorMessage(`video size should be less than ${AppConstants.DEFAULT_UPLOAD_VIDEO_SIZE}MB`);
                }
            }
            else {
                setMediaErrorMessage("file format should be mp4")
            }
        }

    }
    const validateThumbnailVideo = (mediaType) => {
        let validMedia = false;
        if (mediaType === "video/mp4") {
            validMedia = true;
        }
        return validMedia;
    }
    const updateClipBtn = () => {
        if (formValid()) {
            dispatch(showLoader());
            updateEpisodeClipData()
        }
    }
    const editClipBtn = () => {
        setInputEditable(true);
    }
    const cancelRowClip = () => {
        setInputEditable(false);
    }
    const formValid = () => {
        let isValid = true;
        if (clipItemSeq == -1) {
            if (clipFile == null) {
                isValid = false;
                setMediaErrorMessage(`Please add a clip`);
            }
        }
        if (!checkValueLength(clipTitle)) {
            isValid = false;
            setMediaErrorMessage(`Title is required`)
        }
        return isValid;
    }
    const submitEpisodeClipData = () => {
        let imageHasMap = [];
        if (clipFile != null) {
            imageHasMap.push({ inputName: "clip_file", imageData: clipFile });
        }
        let titleVal = encodeURIComponent(clipTitle);
        let fileFormat = "MP4";
        let postType = "FREE";
        const profileSeq = getCurrentUserProfileSeq();

        AddEpisodeClipApi(episodeSeq, profileSeq, titleVal, fileFormat, postType, imageHasMap).then(result => {
            dispatch(closeLoader());
            if (result.isSuccess) {
                dispatch(openSnackBar({
                    open: true,
                    message: result.message,
                    type: "SUCCESS"
                }));
                setClipItemSeq(result.data.data.post_seq);
                setInputEditable(false);
            }
            else {
                let errorMsgShow = false;
                if (result.data && result.data.err_cd == "E006" && result.data.data) {
                    if (result.data.data.comments) {
                        setMediaErrorMessage(result.data.data.comments)
                        errorMsgShow = true;
                    }
                    if (result.data.data.profile_seq) {
                        dispatch(openSnackBar({
                            open: true,
                            message: result.data.data.profile_seq,
                            type: "FAILED"

                        }))
                        errorMsgShow = true;
                    }
                    if (result.data.data.episode_seq) {
                        dispatch(openSnackBar({
                            open: true,
                            message: result.data.data.episode_seq,
                            type: "FAILED"

                        }))
                        errorMsgShow = true;
                    }
                    if (result.data.data.file_format) {
                        dispatch(openSnackBar({
                            open: true,
                            message: result.data.data.file_format,
                            type: "FAILED"

                        }))
                        errorMsgShow = true;
                    }
                    if (result.data.data.post_type) {
                        dispatch(openSnackBar({
                            open: true,
                            message: result.data.data.post_type,
                            type: "FAILED"

                        }))
                        errorMsgShow = true;
                    }
                    if (result.data.data.media_type) {
                        dispatch(openSnackBar({
                            open: true,
                            message: result.data.data.media_type,
                            type: "FAILED"

                        }))
                        errorMsgShow = true;
                    }
                }
                if (!errorMsgShow) {
                    dispatch(openSnackBar({
                        open: true,
                        message: result.message,
                        type: "FAILED"

                    }))
                }
            }
        });
    }
    const updateEpisodeClipData = () => {
        updateEpisodeClipApi(clipItemSeq, clipTitle).then(result => {
            dispatch(closeLoader());
            if (result.isSuccess) {
                dispatch(openSnackBar({
                    open: true,
                    message: result.message,
                    type: "SUCCESS"
                }));
                setInputEditable(false);
            }
            else {
                let errorMsgShow = false;
                if (result.data && result.data.err_cd == "E006" && result.data.data) {
                    if (result.data.data.comments) {
                        setMediaErrorMessage(result.data.data.comments)
                        errorMsgShow = true;
                    }
                    if (result.data.data.post_seq) {
                        dispatch(openSnackBar({
                            open: true,
                            message: result.data.data.post_seq,
                            type: "FAILED"
                        }))
                        errorMsgShow = true;
                    }
                }
                if (!errorMsgShow) {
                    dispatch(openSnackBar({
                        open: true,
                        message: result.message,
                        type: "FAILED"

                    }))
                }
            }
        });

    }
    const handleConfirmationClose = () => {
        setConfirmation(prevState => ({
            ...prevState, open: false
        }));
    }
    const deleteRowClip = () => {
        setConfirmation({
            open: true,
            title: "Confirmation",
            message: "Do you want to delete this Clip?",
            onSubmit: () => {
                deleteEpisodeClipData();
            }
        });
    }
    const deleteEpisodeClipData = () => {
        setEpisodeClipStatusApi(clipItemSeq, "INACTIVE").then(result => {
            dispatch(closeLoader());
            if (result.isSuccess) {
                dispatch(openSnackBar({
                    open: true,
                    message: result.message,
                    type: "SUCCESS"
                }));
                clipRowBtnPress("DELETE_CLIP", { index: index, clipSeq: clipItemSeq })
            }
            else {
                let errorMsgShow = false;
                if (result.data && result.data.err_cd == "E006" && result.data.data) {
                    if (result.data.data.status) {
                        setMediaErrorMessage(result.data.data.status)
                        errorMsgShow = true;
                    }
                    if (result.data.data.post_seq) {
                        dispatch(openSnackBar({
                            open: true,
                            message: result.data.data.post_seq,
                            type: "FAILED"
                        }))
                        errorMsgShow = true;
                    }
                }
                if (!errorMsgShow) {
                    dispatch(openSnackBar({
                        open: true,
                        message: result.message,
                        type: "FAILED"

                    }))
                }
            }
        });

    }
    const ButtonIcon = ({ icon = null }) => {
        return <img src={icon} className='buttonSmallIcon me-1' alt='button icon' />
    }
    return (
        <div className=' mb-3'>
            <div className='clipItemBox d-flex align-items-center '>
                <Grid container spacing={2}>
                    <Grid item xs={2} md={2} className='d-flex align-items-center'>
                        <div className='clipItemBox__itemTitle'>Clip {index + 1}</div>
                    </Grid>
                    <Grid item xs={12} md={5}>
                        <div className='d-flex align-items-center'>
                            <div className='clipItemBox__itemTitle'>Title</div>
                            <div className='clipItemBox__inputBox'>
                                <input type="text" value={clipTitle}
                                    onChange={(e) => handleClipTitle(e)}
                                    disabled={!inputEditable}
                                    className='clipItemBox__input' placeholder='Title' />
                            </div>
                        </div>
                    </Grid>
                    <Grid item xs={12} md={5} pr={2}>
                        <input
                            ref={mediaRefPress}
                            className="d-none"
                            onClick={(event) => {
                                event.target.value = null
                            }}
                            type="file"
                            accept="video/mp4"
                            onChange={(e) => onInputChange(e)} />
                        {
                            clipItemSeq == -1 ?
                                <>
                                    <div className='d-flex justify-content-center align-items-center'>
                                        {
                                            clipDisplayFile == null ?
                                                <div className='uploadClipBtn d-flex align-items-center 
                                                justify-content-center'
                                                    onClick={() => selectClipFile()}>
                                                    <ButtonIcon icon={SELECT_MEDIA_ICON} />
                                                    <div className='uploadClipBtn__text'>Select Clip</div>
                                                </div>
                                                :
                                                <div className='me-2'>
                                                    <div className='uploadClipBtn d-flex align-items-center justify-content-center editBtn'
                                                        onClick={() => selectClipFile()}>
                                                        <div className='uploadClipBtn__text'>Change</div>
                                                    </div>

                                                </div>
                                        }
                                        {
                                            clipFile != null ?
                                                <div className='uploadClipBtn d-flex align-items-center justify-content-center'
                                                    onClick={() => uploadClipBtnPress()}>
                                                    <div className='uploadClipBtn__text'>Upload Clip</div>
                                                </div>
                                                : null
                                        }


                                    </div>
                                </>
                                :
                                <>
                                    <div className='d-flex justify-content-center align-items-center'>
                                        {
                                            inputEditable ?
                                                <div className='uploadClipBtn d-flex align-items-center justify-content-center
                                            me-2 editBtn '
                                                    onClick={() => updateClipBtn()}>
                                                    <div className='uploadClipBtn__text'>Update</div>
                                                </div>
                                                :
                                                <div className='uploadClipBtn d-flex align-items-center justify-content-center
                                            me-2 editBtn '
                                                    onClick={() => editClipBtn()}>
                                                    <ButtonIcon icon={EDIT_ICON} />
                                                    <div className='uploadClipBtn__text'>Edit</div>
                                                </div>
                                        }
                                        {
                                            inputEditable ?
                                                <div className='uploadClipBtn d-flex align-items-center justify-content-center'
                                                    onClick={() => cancelRowClip()}>

                                                    <div className='uploadClipBtn__text'>Cancel</div>
                                                </div>
                                                :
                                                <div className='uploadClipBtn d-flex align-items-center justify-content-center'
                                                    onClick={() => deleteRowClip()}>
                                                    <ButtonIcon icon={DELETE_ICON} />
                                                    <div className='uploadClipBtn__text'>Delete</div>
                                                </div>
                                        }
                                    </div>
                                </>


                        }
                    </Grid>
                </Grid>
                {
                    clipItemSeq == -1 ?
                        <div className='clipItemBox__crossBox d-flex align-items-center justify-content-center'>
                            <div className='clipItemBox__crossBox__cross' onClick={() => handleCrossClick()}>
                                {/* <DeleteForeverRoundedIcon /> */}
                                <ButtonIcon icon={DELETE_ICON} />
                            </div>
                        </div>
                        : null
                }
            </div>
            <div className='ps-2'>
                <FormHelperText error={true}>{mediaErrorMessage}</FormHelperText>
            </div>
            <AppConfirmation
                open={confirmation.open}
                onClose={handleConfirmationClose}
                onSubmit={confirmation.onSubmit}
                title={confirmation.title}
                message={confirmation.message} />
        </div>
    )
}

export default ClipItemRow