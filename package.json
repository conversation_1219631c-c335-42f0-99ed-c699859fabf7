{"name": "sotrue-playlist", "version": "0.1.0", "private": true, "homepage": ".", "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.16.0", "@mui/material": "^5.16.0", "@mui/x-charts": "^7.9.0", "@mui/x-date-pickers": "^7.11.0", "@reduxjs/toolkit": "^2.2.6", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "bootstrap": "^5.3.3", "dayjs": "^1.11.12", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.1.2", "react-router-dom": "^6.4.1", "react-scripts": "5.0.1", "react-select": "^5.8.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}